# Upload Chapter Images Command

## M<PERSON> tả
Command Laravel để upload hình ảnh chương từ thư mục local (`storage/app/public/dcn` và `storage/app/public/images/data`) lên S3 storage với cấu trúc thư mục được tổ chức hợp lý. Command sẽ tìm chapters có URLs từ domain `wa.hakurl.com` và upload các file tương ứng từ storage local.

## Cài đặt
Command đã được tạo tại: `app/Console/Commands/UploadChapterImagesCommand.php`

## Cách sử dụng

### 1. Quét toàn bộ database tìm chapters chứa 'wa.hakurl.com'
```bash
php artisan upload:chapter-images
```

### 2. Upload ảnh cho một chapter cụ thể
```bash
php artisan upload:chapter-images {chapter_id}
```

Ví dụ:
```bash
php artisan upload:chapter-images 123
```

### 3. Upload ảnh cho tất cả chapters của một manga
```bash
php artisan upload:chapter-images --manga_id=456
```

### 4. <PERSON><PERSON><PERSON> thử nghiệm (không thực sự upload)
```bash
php artisan upload:chapter-images --dry-run
```

### 5. Tùy chỉnh batch size
```bash
php artisan upload:chapter-images --batch-size=20
```

## Tham số

### Arguments
- `chapter_id` (optional): ID của chapter cụ thể cần upload

### Options
- `--manga_id=`: ID của manga để upload tất cả chapters
- `--dry-run`: Chạy thử nghiệm, không thực sự upload
- `--batch-size=10`: Số lượng ảnh xử lý cùng lúc (mặc định: 10)

## Chức năng

### 1. Lọc URL và File Local
- Chỉ xử lý URLs có domain `wa.hakurl.com`
- Tìm file tương ứng trong `storage/app/public/dcn` hoặc `storage/app/public/images/data`
- Bỏ qua các URL không có file local

### 2. Cấu trúc thư mục S3
```
chapters/
├── {chapter_id}/
│   └── images/
│       ├── image1.jpg
│       ├── image2.jpg
│       └── ...
```

### 3. Xử lý lỗi
- Kiểm tra file tồn tại trong storage local
- Log chi tiết các lỗi và đường dẫn file
- Comprehensive error handling

### 4. Theo dõi tiến trình
- Hiển thị progress bar
- Thống kê real-time
- Báo cáo cuối cùng

## Ví dụ Output

```
📚 Tìm thấy 1 chapter(s) để xử lý

📖 Xử lý Chapter: One Piece - Chapter 1000
🔍 Tìm thấy 15 ảnh từ wa.hakurl.com
📦 Xử lý batch 1/2
✅ Upload thành công: 001.jpg
✅ Upload thành công: 002.jpg
⏭️  Bỏ qua: 003.jpg
📦 Xử lý batch 2/2
✅ Upload thành công: 004.jpg
❌ Thất bại: 005.jpg - Network timeout

✅ Đã cập nhật content cho chapter 123

📊 THỐNG KÊ CUỐI:
+------------------+-----------+
| Trạng thái       | Số lượng  |
+------------------+-----------+
| Đã xử lý         | 15        |
| Upload thành công| 13        |
| Bỏ qua           | 1         |
| Thất bại         | 1         |
+------------------+-----------+

🎉 Hoàn thành! Đã upload 13 ảnh lên S3
```

## Cấu hình S3

Command sử dụng cấu hình S3 từ `config/filesystems.php`:

```php
's3' => [
    'driver' => 's3',
    'key' => env('AWS_ACCESS_KEY_ID'),
    'secret' => env('AWS_SECRET_ACCESS_KEY'),
    'region' => env('AWS_DEFAULT_REGION'),
    'bucket' => env('AWS_BUCKET'),
    'url' => env('AWS_URL'),
    'endpoint' => env('AWS_ENDPOINT'),
    'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
],
```

## Cập nhật Database

Sau khi upload thành công, command sẽ:
1. Thay thế URLs `wa.hakurl.com` bằng URLs S3 mới
2. Cập nhật field `content` trong bảng `chapters`
3. Giữ nguyên các URLs khác

## Lưu ý

### Hiệu suất
- Sử dụng batch processing để tránh quá tải
- Timeout 30 giây cho mỗi download
- Kiểm tra file tồn tại trước khi upload

### Bảo mật
- Chỉ download từ domain `wa.hakurl.com`
- Validate file types (images only)
- Proper error handling

### Monitoring
- Log chi tiết trong Laravel logs
- Progress tracking
- Comprehensive error reporting

## Troubleshooting

### Lỗi thường gặp

1. **Network timeout**
   - Tăng batch-size nhỏ hơn
   - Kiểm tra kết nối internet

2. **S3 permission denied**
   - Kiểm tra AWS credentials
   - Verify bucket permissions

3. **Memory limit**
   - Giảm batch-size
   - Tăng PHP memory limit

### Debug mode
```bash
php artisan upload:chapter-images 123 --dry-run -v
```
