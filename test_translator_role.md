# Test Translator Role Implementation

## C<PERSON><PERSON> thay đổi đã thực hiện:

### 1. <PERSON><PERSON><PERSON> nhật RoleEnum
- Thêm role `Translator = 'translator'` vào `app/Enums/RoleEnum.php`

### 2. Tạo TranslatorAuth Middleware
- File: `app/Http/Middleware/TranslatorAuth.php`
- Chức năng:
  - Cho phép Admin truy cập tất cả
  - Ki<PERSON><PERSON> tra user có role `translator`
  - <PERSON> phép GET requests (index, show) 
  - Kiểm tra ownership cho POST/PUT/DELETE requests
  - Xử lý bulk operations (chapters-order, delete-many)

### 3. Cập nhật AuthService
- File: `app/Services/AdminApi/AuthService.php`
- <PERSON> phép cả `admin` và `translator` login
- Tr<PERSON> về role trong response

### 4. Cập nhật Kernel
- Đăng ký middleware `translatorAuth` trong `app/Http/Kernel.php`

### 5. Cập nhật Routes
- File: `routes/api.php`
- Áp dụng middleware `translatorAuth` cho:
  - Manga routes
  - Chapter routes (bao gồm các route đặc biệt)

### 6. C<PERSON><PERSON> nhật Services
- `app/Services/AdminApi/MangaService.php`: Filter manga theo user_id cho translator
- `app/Services/AdminApi/ChapterService.php`: Filter chapter theo user_id cho translator

### 7. Migration
- File: `database/migrations/2025_06_16_121139_add_translator_role.php`
- Tạo role `translator` với guard_name `user`

## Cách test:

### 1. Tạo user với role translator:
```php
$user = User::create([
    'name' => 'Translator Test',
    'email' => '<EMAIL>',
    'password' => 'password'
]);
$user->assignRole('translator');
```

### 2. Test login API:
```bash
POST /api/admin/auth
{
    "email": "<EMAIL>",
    "password": "password"
}
```

### 3. Test access với token:
```bash
# Chỉ thấy manga/chapter của mình
GET /api/admin/mangas
Authorization: Bearer {token}

# Chỉ edit được manga/chapter của mình
PUT /api/admin/mangas/{id}
Authorization: Bearer {token}
```

## Quyền hạn Translator:
- ✅ Login vào admin API
- ✅ Xem danh sách manga/chapter của mình
- ✅ Tạo manga/chapter mới
- ✅ Chỉnh sửa manga/chapter của mình
- ✅ Xóa manga/chapter của mình
- ❌ Không thể truy cập manga/chapter của người khác
- ❌ Không thể truy cập các API khác (users, groups, etc.)
