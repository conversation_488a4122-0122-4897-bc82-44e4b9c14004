<?php

use App\Http\Controllers\AdminApi\AchievementController;
use App\Http\Controllers\AdminApi\ArtistController;
use App\Http\Controllers\AdminApi\AuthController;
use App\Http\Controllers\AdminApi\ChapterController;
use App\Http\Controllers\AdminApi\CommentController;
use App\Http\Controllers\AdminApi\DoujinshiController;
use App\Http\Controllers\AdminApi\GenreController;
use App\Http\Controllers\AdminApi\GroupController;
use App\Http\Controllers\AdminApi\MangaController;
use App\Http\Controllers\AdminApi\PetController;
use App\Http\Controllers\AdminApi\StaticController;
use App\Http\Controllers\AdminApi\UserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('admin')->group(function () {
    // Bỏ giới hạn tốc độ truy cập cho tất cả API admin
    Route::group(['excluded_middleware' => 'throttle:api'], function() {
        /** Admin Auth */
        Route::post('auth', [AuthController::class, 'login']);
        Route::get('auth', [AuthController::class, 'profile']);
        Route::delete('auth', [AuthController::class, 'logout']);

        /** Users */
        Route::apiResource('users', UserController::class);
        Route::delete('users/{id}/delete-comment', [UserController::class, 'deleteComment']);

        /** Manga */
        Route::apiResource('mangas', MangaController::class);

        /** Chapters */
        Route::delete('chapters/{id}/clr-img', [ChapterController::class, 'clearImage']);
        Route::put('chapters/{id}/add-img', [ChapterController::class, 'addImage']);
        Route::put('chapters/chapters-order', [ChapterController::class, 'updateChapterOrder']);
        Route::put('chapters/delete-many', [ChapterController::class, 'deleteMany']);
        Route::apiResource('chapters', ChapterController::class);

        /** Groups */
        Route::apiResource('groups', GroupController::class);

        /** Artists */
        Route::apiResource('artists', ArtistController::class);

        /** Doujinshi */
        Route::apiResource('doujinshis', DoujinshiController::class);

        /** Achievements */
        Route::apiResource('achievements', AchievementController::class);

        /** Pets */
        Route::apiResource('pets', PetController::class);

        /** Genre */
        Route::apiResource('genres', GenreController::class);

        /** Comments */
        Route::apiResource('comments', CommentController::class);

        /** Static */
        Route::prefix('statics')->group(function () {
            Route::get('basic', [StaticController::class, 'basicStatic']);
            Route::get('announcement', [StaticController::class, 'getAnnouncement']);
            Route::post('announcement', [StaticController::class, 'updateAnnouncement']);
        });
    });
});


Route::prefix('leech')->middleware(['leechAuth', 'throttle:9999,1'])->group(function () {
    Route::post('mangas', [\App\Http\Controllers\LeechApi\MangaController::class, 'store']);
    Route::post('chapters', [\App\Http\Controllers\LeechApi\ChapterController::class, 'store']);
});

// Public API routes 
Route::prefix('public')->group(function () {
    /** Manga */
    Route::get('mangas', [MangaController::class, 'index']);
    Route::get('mangas/{id}', [MangaController::class, 'show']);
    
    /** Chapters */
    Route::get('chapters', [ChapterController::class, 'index']); 
    Route::get('chapters/{id}', [ChapterController::class, 'show']);
    
    /** Artists */
    Route::get('artists', [ArtistController::class, 'index']);
    Route::get('artists/{id}', [ArtistController::class, 'show']);
    
    /** Doujinshi */
    Route::get('doujinshis', [DoujinshiController::class, 'index']);
    Route::get('doujinshis/{id}', [DoujinshiController::class, 'show']);
    
    /** Genres */
    Route::get('genres', [GenreController::class, 'index']);
    Route::get('genres/{id}', [GenreController::class, 'show']);
    
    /** Comments */
    Route::get('comments', [CommentController::class, 'index']);
    Route::get('comments/{id}', [CommentController::class, 'show']);
});
