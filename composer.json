{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.4 | ^8.0", "ext-json": "*", "aws/aws-sdk-php": "^3.204", "bensampo/laravel-enum": "^4.0", "cviebrock/eloquent-sluggable": "^8.0", "devrabiul/laravel-toaster-magic": "^1.0", "flugger/laravel-responder": "^3.1", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "intervention/image": "^2.7", "jenssegers/agent": "^2.6", "laravel/framework": "^8.65", "laravel/sanctum": "^2.12", "laravel/tinker": "^2.5", "laravel/ui": "^3.4", "league/flysystem-aws-s3-v3": "1.0.25", "league/flysystem-sftp": "^1.0", "livewire/livewire": "2.10", "mews/purifier": "^3.3", "qcod/laravel-imageup": "^1.2", "renatomarinho/laravel-page-speed": "^2.0", "samdark/sitemap": "^2.4", "spatie/laravel-honeypot": "^3.0", "spatie/laravel-permission": "^5.4", "spatie/laravel-query-builder": "^4.0", "spatie/laravel-referer": "*", "spatie/schema-org": "^3.9", "symfony/dom-crawler": "^5.4"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}