<?php

namespace App\Console\Commands;

use App\Services\Leech\Drivers\LxMangaCrawler;
use App\Services\Leech\Drivers\Manhwaclan;
use App\Services\Leech\Drivers\SayhentaiCrawler;
use App\Services\Leech\Drivers\TruyenvnCrawler;
use App\Services\Leech\Drivers\TruyenhentaivnCrawler;
use App\Services\Leech\Drivers\VyvyCrawler;
use App\Services\Leech\Drivers\TiemsachnhoCrawler;
use App\Services\Leech\Drivers\HentaiCubeCrawler;
use App\Services\Leech\MangaProcessor;
use Illuminate\Console\Command;

class MangaCrawler extends Command
{
    protected $signature = 'manga:crawler
                            {source=lxmanga : Nguồn crawl (lxmanga, nettruyenco, ...)} 
                            {startPage=1 : Trang bắt đầu} 
                            {endPage=1 : Trang kết thúc} 
                            {--url= : URL cụ thể của manga cần crawl} 
                            {--proxy= : Danh sách proxy, phân cách bởi dấu phẩy} 
                            {--proxy-file=proxies.txt : File chứa danh sách proxy (mặc định: proxies.txt)} 
                            {--user-agent= : Danh sách User-Agent, phân cách bởi dấu phẩy} 
                            {--user-agent-file= : File chứa danh sách User-Agent} 
                            {--storage-type=s3 : Kiểu lưu trữ (public, s3, sftp hoặc hotlink)}
                            {--no-proxy-images : Không sử dụng proxy khi tải hình ảnh}
                            {--no-resize : Không resize ảnh}
                            {--resize-width=950 : Chiều rộng ảnh khi resize (mặc định: 950px)}
                            {--no-compress : Không nén ảnh}
                            {--compress-quality=90 : Chất lượng ảnh khi nén (1-100, mặc định: 90%)}
                            {--retry-failed : Thử lại các manga đã bị lỗi trước đó}
                            {--failed-log=storage/logs/failed_manga_urls.log : Đường dẫn đến file log chứa các URL lỗi}';

    protected $description = 'Crawl manga data from various sources (new version)';

    public function handle()
    {
        ini_set('memory_limit', '18512M');
        
        $source = $this->argument('source');
        $startPage = $this->argument('startPage');
        $endPage = $this->argument('endPage');
        $url = $this->option('url');
        $retryFailed = $this->option('retry-failed');
        $failedLogPath = $this->option('failed-log');
        
        // Khởi tạo MangaProcessor
        $processor = new MangaProcessor($this);
        
        // Khởi tạo crawler tương ứng với nguồn
        $crawler = $this->createCrawler($source, $processor);
        
        if (!$crawler) {
            $this->error("Không hỗ trợ nguồn: {$source}");
            return 1;
        }
        
        // Thiết lập các tùy chọn cho crawler
        $this->setupCrawlerOptions($crawler);
        
        // Xử lý tùy chọn retry manga bị lỗi
        if ($retryFailed) {
            return $this->handleRetryFailed($crawler, $failedLogPath);
        }
        
        // Thực hiện crawl bình thường
        if ($url) {
            $this->info("[CRAWL] URL: {$url}");
            $crawler->crawlFromUrl($url, $this->option('storage-type'));
        } else {
            $this->info("[CRAWL] Trang {$startPage} - trang {$endPage}: {$source}");
            $crawler->crawlFromPage($startPage, $endPage, $this->option('storage-type'));
        }
        
        $this->info('Đã hoàn thành crawl manga.');
        return 0;
    }
    
    /**
     * Xử lý thử lại các manga đã bị lỗi
     */
    private function handleRetryFailed($crawler, $failedLogPath)
    {
        // Kiểm tra xem file log có tồn tại không
        if (!file_exists($failedLogPath)) {
            $this->error("File log manga lỗi không tồn tại: {$failedLogPath}");
            return 1;
        }
        
        // Trước khi thực hiện, set đường dẫn file log cho crawler
        $crawler->setFailedMangaUrlsFile($failedLogPath);
        
        // Đọc danh sách URL từ file log
        $failedUrls = $crawler->getFailedMangaUrls();
        
        if (empty($failedUrls)) {
            $this->info("Không có URL nào cần xử lý lại trong file: {$failedLogPath}");
            return 0;
        }
        
        $this->info("Tìm thấy " . count($failedUrls) . " URL cần xử lý lại.");
        
        $count = 0;
        $total = count($failedUrls);
        
        // Xử lý từng URL một
        foreach ($failedUrls as $url) {
            $count++;
            $this->info("\n[RETRY] ({$count}/{$total}) URL: {$url}");
            
            // Gọi hàm crawl từ URL
            $crawler->crawlFromUrl($url, $this->option('storage-type'));
        }
        
        $this->info("Đã hoàn thành xử lý lại {$count} URL.");
        return 0;
    }
    
    /**
     * Tạo crawler tương ứng với nguồn
     */
    private function createCrawler($source, $processor)
    {
        switch (strtolower($source)) {
            case 'lxmanga':
                return new LxMangaCrawler($this, $processor);
            case 'manhwaclan':
                return new Manhwaclan($this, $processor);
            case 'sayhentai':
                return new SayhentaiCrawler($this, $processor);
            case 'truyenvn':
                return new TruyenvnCrawler($this, $processor);
            case 'truyenhentaivn':
                return new TruyenhentaivnCrawler($this, $processor);
            case 'vyvy':
                return new VyvyCrawler($this, $processor);
            case 'tiemsachnho':
                return new TiemsachnhoCrawler($this, $processor);
            case 'hentai-cube':
                return new HentaiCubeCrawler($this, $processor);
            default:
                return null;
        }
    }
    
    /**
     * Thiết lập các tùy chọn cho crawler
     */
    private function setupCrawlerOptions($crawler)
    {
        // Thiết lập kiểu lưu trữ
        if ($storageType = $this->option('storage-type')) {
            $crawler->setStorageType($storageType);
        }
        
        // Thiết lập proxy
        if ($proxyOption = $this->option('proxy')) {
            $proxies = explode(',', $proxyOption);
            $crawler->setProxies($proxies);
            $this->info('Sử dụng ' . count($proxies) . ' proxy từ tham số command line');
        }
        
        // Thiết lập proxy từ file
        if ($proxyFile = $this->option('proxy-file')) {
            if (file_exists($proxyFile)) {
                $fileProxies = file($proxyFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                if (!empty($fileProxies)) {
                    $crawler->setProxies($fileProxies);
                    $this->info('Sử dụng ' . count($fileProxies) . ' proxy từ file: ' . $proxyFile);
                }
            } else {
                $this->error('File proxy không tồn tại: ' . $proxyFile);
            }
        }
        
        // Thiết lập User-Agent
        if ($userAgentOption = $this->option('user-agent')) {
            $userAgents = explode(',', $userAgentOption);
            $crawler->setUserAgents($userAgents);
            $this->info('Sử dụng ' . count($userAgents) . ' User-Agent từ tham số command line');
        }
        
        // Thiết lập User-Agent từ file
        if ($userAgentFile = $this->option('user-agent-file')) {
            if (file_exists($userAgentFile)) {
                $fileUserAgents = file($userAgentFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                if (!empty($fileUserAgents)) {
                    $crawler->setUserAgents($fileUserAgents);
                    $this->info('Sử dụng ' . count($fileUserAgents) . ' User-Agent từ file: ' . $userAgentFile);
                }
            } else {
                $this->error('File User-Agent không tồn tại: ' . $userAgentFile);
            }
        }
        
        // Thiết lập có sử dụng proxy cho tải ảnh hay không
        if ($this->option('no-proxy-images')) {
            $crawler->setUseProxyForImages(false);
        }
        
        // Thiết lập cấu hình resize ảnh
        $resizeImages = !$this->option('no-resize');
        $resizeWidth = (int) $this->option('resize-width');
        $crawler->setResizeImages($resizeImages, $resizeWidth);
        
        // Thiết lập cấu hình nén ảnh
        $compressImages = !$this->option('no-compress');
        $compressQuality = (int) $this->option('compress-quality');
        $crawler->setCompressImages($compressImages, $compressQuality);
    }
} 
