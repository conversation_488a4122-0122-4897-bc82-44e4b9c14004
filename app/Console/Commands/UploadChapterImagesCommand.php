<?php

namespace App\Console\Commands;

use App\Models\Chapter;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Exception;

class UploadChapterImagesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'upload:chapter-images
                          {chapter_id? : ID của chapter cụ thể để upload}
                          {--manga_id= : ID của manga để upload tất cả chapters}
                          {--dry-run : Chạy thử nghiệm không thực sự upload}
                          {--batch-size=30 : Số lượng ảnh xử lý cùng lúc}';

    /**
     * The console command description.
     */
    protected $description = 'Upload chapter images từ dcn.hakurl.com lên S3 storage';

    /**
     * Thống kê quá trình upload
     */
    private $stats = [
        'processed' => 0,
        'uploaded' => 0,
        'failed' => 0,
        'skipped' => 0,
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $chapterId = $this->argument('chapter_id');
        $mangaId = $this->option('manga_id');
        $isDryRun = $this->option('dry-run');
        $batchSize = (int) $this->option('batch-size');

        if ($isDryRun) {
            $this->info('🧪 CHẾ ĐỘ THỬ NGHIỆM - Không thực sự upload');
        }

        // Xây dựng query
        $query = Chapter::query();

        if ($chapterId) {
            $query->where('id', $chapterId);
        } elseif ($mangaId) {
            $query->where('manga_id', $mangaId);
        } else {
            // Quét toàn bộ database tìm chapters chứa 'dcn.hakurl.com'
            $query->where('content', 'LIKE', '%dcn.hakurl.com%');
        }

        $chapters = $query->with('manga')->get();

        if ($chapters->isEmpty()) {
            $this->error('❌ Không tìm thấy chapter nào!');
            return 1;
        }

        $this->info("📚 Tìm thấy {$chapters->count()} chapter(s) để xử lý");

        // Xử lý từng chapter
        foreach ($chapters as $chapter) {
            $this->processChapter($chapter, $isDryRun, $batchSize);
        }

        // Hiển thị thống kê cuối
        $this->displayFinalStats();

        return 0;
    }

    /**
     * Xử lý một chapter
     */
    private function processChapter(Chapter $chapter, bool $isDryRun, int $batchSize): void
    {
        $this->info("\n📖 Xử lý Chapter: {$chapter->manga->name} - {$chapter->name}");

        $content = $chapter->getRawOriginal('content');
        if (empty($content)) {
            $this->warn('⚠️  Chapter không có nội dung');
            return;
        }

        // Lấy danh sách URLs từ content
        $urls = $this->extractUrls($content);

        if (empty($urls)) {
            $this->warn('⚠️  Không tìm thấy URL dcn.hakurl.com nào');
            return;
        }

        $this->info("🔍 Tìm thấy {$urls->count()} ảnh từ dcn.hakurl.com");

        // Xử lý theo batch
        $batches = $urls->chunk($batchSize);
        $newUrls = collect();

        foreach ($batches as $batchIndex => $batch) {
            $this->info("📦 Xử lý batch " . ($batchIndex + 1) . "/{$batches->count()}");

            $batchResults = $this->processBatch($batch, $chapter->id, $isDryRun);
            $newUrls = $newUrls->merge($batchResults);
        }

        // Cập nhật content nếu không phải dry run
        if (!$isDryRun && $newUrls->isNotEmpty()) {
            $this->updateChapterContent($chapter, $newUrls);
        }
    }

    /**
     * Trích xuất URLs từ content
     */
    private function extractUrls(string $content): \Illuminate\Support\Collection
    {
        $lines = explode(PHP_EOL, $content);

        return collect($lines)
            ->map(fn($line) => trim($line))
            ->filter(fn($line) => !empty($line))
            ->filter(fn($line) => Str::contains($line, 'dcn.hakurl.com'));
    }

    /**
     * Xử lý một batch ảnh
     */
    private function processBatch(\Illuminate\Support\Collection $urls, string $chapterId, bool $isDryRun): \Illuminate\Support\Collection
    {
        $results = collect();

        foreach ($urls as $url) {
            $this->stats['processed']++;

            $result = $this->processImage($url, $chapterId, $isDryRun);

            if ($result['status'] === 'success') {
                $results->push($result['newUrl']);
                $this->stats['uploaded']++;
                $this->line("✅ Upload thành công: " . basename($url));
            } elseif ($result['status'] === 'skipped') {
                $results->push($result['newUrl']);
                $this->stats['skipped']++;
                $this->line("⏭️  Bỏ qua: " . basename($url));
            } else {
                $this->stats['failed']++;
                $this->error("❌ Thất bại: " . basename($url) . " - " . ($result['message'] ?? 'Unknown error'));
            }
        }

        return $results;
    }

    /**
     * Xử lý một ảnh đơn lẻ
     */
    private function processImage(string $url, string $chapterId, bool $isDryRun): array
    {
        try {
            // Tạo đường dẫn S3
            $filename = basename(parse_url($url, PHP_URL_PATH));
            $s3Path = "chapters/{$chapterId}/images/{$filename}";
            $newUrl = $this->generateS3Url($s3Path);

            // Tìm file local từ URL
            $localPath = $this->getLocalPathFromUrl($url);

            if (!$localPath) {
                return [
                    'status' => 'failed',
                    'message' => "Local file not found for URL: {$url}"
                ];
            }

            // Kiểm tra file tồn tại (có thể là absolute path hoặc relative path)
            $fileExists = false;
            if (str_starts_with($localPath, '/')) {
                // Absolute path
                $fileExists = file_exists($localPath);
            } else {
                // Relative path trong storage
                $fileExists = Storage::disk('public')->exists($localPath);
            }

            if (!$fileExists) {
                return [
                    'status' => 'failed',
                    'message' => "Local file not found: {$localPath}"
                ];
            }

            if ($isDryRun) {
                return [
                    'status' => 'success',
                    'newUrl' => $newUrl,
                    'message' => "Dry run - would upload from {$localPath}"
                ];
            }

            // Đọc file từ đường dẫn thực tế hoặc storage
            if (str_starts_with($localPath, '/')) {
                // Đọc từ absolute path
                $imageContent = file_get_contents($localPath);
            } else {
                // Đọc từ storage local
                $imageContent = Storage::disk('public')->get($localPath);
            }

            if (!$imageContent) {
                return [
                    'status' => 'failed',
                    'message' => 'Failed to read local file'
                ];
            }

            // Upload lên S3
            Storage::disk('s3')->put($s3Path, $imageContent, 'public');

            return [
                'status' => 'success',
                'newUrl' => $newUrl,
                'message' => "Successfully uploaded from {$localPath}"
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Chuyển đổi URL thành đường dẫn local file
     */
    private function getLocalPathFromUrl(string $url): ?string
    {
        // Ví dụ URL: https://wa.hakurl.com/dcn/manga/123/chapter/456/01.jpg
        // Đường dẫn file: /www/wwwroot/dcn.saikomangaraw.net/dcn/manga/123/chapter/456/01.jpg

        $parsedUrl = parse_url($url);
        $path = $parsedUrl['path'] ?? '';

        // Loại bỏ leading slash
        $path = ltrim($path, '/');

        // Chuyển đổi từ URL path sang file path thực tế
        // URL: wa.hakurl.com/dcn/manga/123/chapter/456/01.jpg
        // File: /www/wwwroot/dcn.saikomangaraw.net/dcn/manga/123/chapter/456/01.jpg
        $realFilePath = '/www/wwwroot/dcn.saikomangaraw.net/' . $path;

        // Kiểm tra file có tồn tại không
        if (file_exists($realFilePath)) {
            return $realFilePath;
        }

        // Fallback: kiểm tra trong storage/app/public (cho tương thích ngược)
        $possiblePaths = [
            $path,                                    // dcn/manga/123/chapter/456/01.jpg
            'dcn/' . $path,                          // dcn/dcn/manga/123/chapter/456/01.jpg
            'images/data/' . str_replace('dcn/', '', $path), // images/data/manga/123/chapter/456/01.jpg
        ];

        foreach ($possiblePaths as $testPath) {
            if (Storage::disk('public')->exists($testPath)) {
                return $testPath;
            }
        }

        return null;
    }

    /**
     * Tạo URL S3 đầy đủ
     */
    private function generateS3Url(string $s3Path): string
    {
        $baseUrl = config('filesystems.disks.s3.url');

        if ($baseUrl) {
            return rtrim($baseUrl, '/') . '/' . $s3Path;
        }

        // Fallback nếu không có URL config
        $bucket = config('filesystems.disks.s3.bucket');
        return "https://{$bucket}.s3.amazonaws.com/{$s3Path}";
    }

    /**
     * Cập nhật content của chapter
     */
    private function updateChapterContent(Chapter $chapter, \Illuminate\Support\Collection $newUrls): void
    {
        try {
            $content = $chapter->getRawOriginal('content');
            $lines = explode(PHP_EOL, $content);

            $updatedLines = collect($lines)->map(function ($line) use ($newUrls) {
                $line = trim($line);

                if (empty($line) || !Str::contains($line, 'dcn.hakurl.com')) {
                    return $line;
                }

                // Tìm URL mới tương ứng
                $filename = basename(parse_url($line, PHP_URL_PATH));
                $newUrl = $newUrls->first(function ($url) use ($filename) {
                    return Str::endsWith($url, $filename);
                });

                return $newUrl ?: $line;
            });

            $chapter->update([
                'content' => $updatedLines->implode(PHP_EOL)
            ]);

            $this->info("✅ Đã cập nhật content cho chapter {$chapter->id}");

        } catch (Exception $e) {
            $this->error("❌ Lỗi cập nhật content: " . $e->getMessage());
        }
    }

    /**
     * Hiển thị thống kê cuối
     */
    private function displayFinalStats(): void
    {
        $this->info("\n📊 THỐNG KÊ CUỐI:");
        $this->table(
            ['Trạng thái', 'Số lượng'],
            [
                ['Đã xử lý', $this->stats['processed']],
                ['Upload thành công', $this->stats['uploaded']],
                ['Bỏ qua', $this->stats['skipped']],
                ['Thất bại', $this->stats['failed']],
            ]
        );

        if ($this->stats['failed'] > 0) {
            $this->warn("⚠️  Có {$this->stats['failed']} ảnh upload thất bại");
        }

        if ($this->stats['uploaded'] > 0) {
            $this->info("🎉 Hoàn thành! Đã upload {$this->stats['uploaded']} ảnh lên S3");
        }
    }
}
