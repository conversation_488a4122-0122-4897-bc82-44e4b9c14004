<?php

namespace App\Services\Leech\Drivers;

use App\Services\Leech\BaseCrawler;
use Illuminate\Support\Str;

class HentaiCubeCrawler extends BaseCrawler
{
    protected $baseUrl = 'https://hentaicube.xyz'; 

    /**
     * Trả về URL cho trang danh sách manga
     */
    protected function getMangaListUrl($page)
    {
        return '/page/' . $page;
    }

    /**
     * Trích xuất dữ liệu manga từ HTML trang danh sách
     */
    protected function extractMangaData($html)
    {
        $crawler = $this->createDomCrawler($html);

        $mangaData = [];
        $crawler->filter('.post-title .h5 a')->each(function ($node) use (&$mangaData) {
            $mangaData[] = [
                'name' => $node->text(),
                'link' => $node->attr('href'),
            ];
        });

        return $mangaData;
    }

    /**
     * Trích xuất thông tin chi tiết manga từ HTML trang manga
     */
    protected function extractMangaDetails($html)
    {
        $crawler = $this->createDomCrawler($html);

        $name = $crawler->filter('.post-title h1')->text();
        $slug = Str::slug($name, '-', 'en');
        $nameAlt = null;
        $artist = null;
        $status = 2;
        $genreNames = [];

        $pilot = null;

        $chapterLinks = $crawler->filter('.wp-manga-chapter > a')->each(function ($node) {
            return [
                'name' => $node->text(),
                'link' => $node->attr('href')
            ];
        });
        $coverUrl = '';

        $mangaData = [
            'name' => $name,
            'name_alt' => $nameAlt,
            'artist' => $artist,
            'status' => $status,
            'genres' => $genreNames,
            'pilot' => $pilot,
            'chapters' => $chapterLinks,
            'cover_url' => $coverUrl,
            'slug' => $slug,
        ];

        return $mangaData;
    }

    /**
     * Trích xuất thông tin chi tiết chapter từ HTML trang chapter
     */
    protected function extractChapterDetails($html, $chapterInfo)
    {
        $crawler = $this->createDomCrawler($html);

        // Sử dụng thông tin chapter từ danh sách đã có
        $name = $chapterInfo['name'];
        $images = $crawler->filter('.reading-content img')->each(function ($node) {
            return $node->attr('src') ?? $node->attr('data-src');
        });
        
        //remove non-image url and space in url
        $images = array_map(function ($image) {
            return trim(str_replace(' ', '', $image));
        }, $images);

        $chapterContent = [
            'name' => $name,
            'images' => $images,
        ];

        return $chapterContent;
    }
} 