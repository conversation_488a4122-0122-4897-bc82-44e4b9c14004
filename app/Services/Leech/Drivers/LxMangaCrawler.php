<?php

namespace App\Services\Leech\Drivers;

use App\Services\Leech\BaseCrawler;
use Illuminate\Support\Str;

class LxMangaCrawler extends BaseCrawler
{
    protected $baseUrl = 'https://lxmanga.blog'; // URL nguồn LxManga

    /**
     * Trả về URL cho trang danh sách manga
     */
    protected function getMangaListUrl($page)
    {
        return '/danh-sach?page=' . $page;
    }

    /**
     * Trích xuất dữ liệu manga từ HTML trang danh sách
     */
    protected function extractMangaData($html)
    {
        $crawler = $this->createDomCrawler($html);

        $mangaData = [];
        $crawler->filter('div > div.p-2.w-full.truncate > a')->each(function ($node) use (&$mangaData) {
            $mangaData[] = [
                'name' => $node->text(),
                'link' => $this->baseUrl . $node->attr('href'),
            ];
        });

        return $mangaData;
    }

    /**
     * Trích xuất thông tin chi tiết manga từ HTML trang manga
     */
    protected function extractMangaDetails($html)
    {
        $crawler = $this->createDomCrawler($html);

        $name = $crawler->filter('ol > li:nth-child(3) > div > span')->text();
        $slug = Str::slug($name, '-', 'en');
        $nameAlt = null;
        $artist = null;
        $status = 2;
        $genreNames = $crawler->filter('div.grow > div:nth-child(1) > span:nth-child(2) > a')->each(function ($node) {
            return $node->text();
        });

        // Remove genre 'Truyện Màu' from genreNames
        $genreNames = array_diff($genreNames, ['3D']);

        // Check selector is exist
        $pilot = null;
        if ($crawler->filter('div.py-4.border-t.border-gray-200.dark\:border-gray-600 > p:nth-child(3)')->count() > 0) {
            $pilot = $crawler->filter('div.py-4.border-t.border-gray-200.dark\:border-gray-600 > p:nth-child(3)')->text();
        }

        // Check if $pilot contain $name, if yes, set $pilot to null
        if (Str::contains($pilot, $name)) {
            $pilot = null;
        }

        $chapterLinks = $crawler->filter('.justify-between ul.overflow-y-auto.overflow-x-hidden > a')->each(function ($node) {
            return [
                'name' => $node->filter('.text-ellipsis')->text(),
                'link' => $this->baseUrl . $node->attr('href')
            ];
        });

        $style = $crawler->filter('div.rounded-lg.cover')->attr('style');
        preg_match('/url\([\'"]?(.*?)?[\'"]?\)/', $style, $matches);

        $coverUrl = $matches[1] ?? '';

        $mangaData = [
            'name' => $name,
            'name_alt' => $nameAlt,
            'artist' => $artist,
            'status' => $status,
            'genres' => $genreNames,
            'pilot' => $pilot,
            'chapters' => $chapterLinks,
            'cover_url' => $coverUrl,
            'slug' => $slug,
        ];

        return $mangaData;
    }

    /**
     * Trích xuất thông tin chi tiết chapter từ HTML trang chapter
     */
    protected function extractChapterDetails($html, $chapterInfo)
    {
        $crawler = $this->createDomCrawler($html);

        // Sử dụng thông tin chapter từ danh sách đã có
        $name = $chapterInfo['name'];
        $images = $crawler->filter('.text-center #image-container')->each(function ($node) {
            return $node->attr('data-src');
        });

        $chapterContent = [
            'name' => $name,
            'images' => $images,
        ];

        return $chapterContent;
    }
} 
