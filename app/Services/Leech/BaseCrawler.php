<?php

namespace App\Services\Leech;

use GuzzleHttp\Client;
use GuzzleHttp\Promise\Utils;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManagerStatic;
use Illuminate\Console\Command;
use Illuminate\Support\Str;
use App\Models\Chapter;

abstract class BaseCrawler
{
    protected $baseUrl;
    protected $command;
    protected $proxies = [];
    protected $userAgents = [];
    protected $currentProxy = null;
    protected $currentUserAgent = null;
    protected $requestCount = 0;
    protected $maxRequestPerProxy = 50;
    protected $requestDelay = 200;
    protected $failedProxies = [];
    protected $useProxyForImages = true;
    protected $retryWithoutProxy = true;
    protected $processor;
    protected $storageType = 'public'; // Giá trị mặc định là 'public'
    
    // Cấu hình cho resize và nén ảnh
    protected $resizeImages = true; // Mặc định bật resize ảnh
    protected $resizeWidth = 900; // Chiều rộng mặc định khi resize
    protected $compressImages = true; // Mặc định bật nén ảnh
    protected $compressQuality = 90; // Chất lượng mặc định khi nén (1-100)

    // Cấu hình cho URL manga lỗi
    protected $failedMangaUrlsFile = 'storage/logs/failed_manga_urls.log';
    protected $successMangaUrlsFile = 'storage/logs/success_manga_urls.log';
    // Lưu trữ các URL ảnh lỗi 404 để không retry nhiều lần
    protected $notFoundImageUrls = [];
    // Số lần retry tối đa cho một ảnh với proxy khác trước khi thử không dùng proxy
    protected $maxProxyRetries = 2;

    public function __construct(Command $command, MangaProcessor $processor)
    {
        $this->command = $command;
        $this->processor = $processor;
        $this->initDefaultUserAgents();
        
        // Đảm bảo thư mục lưu trữ tồn tại
        $this->ensureStorageDirectoryExists();
    }

    /**
     * Thiết lập kiểu lưu trữ ('s3', 'public', 'sftp', hoặc 'hotlink')
     */
    public function setStorageType(string $storageType)
    {
        if ($storageType === 'public') {
            $this->storageType = 'public'; // Sử dụng disk 'public' cho lưu trữ local
            $this->ensureStorageDirectoryExists();
            $this->command->info("[SETTING] Lưu ảnh vào LOCAL");
        } else if ($storageType === 's3') {
            // Kiểm tra xem S3 disk có khả dụng không
            try {
                if (config('filesystems.disks.s3.key') && config('filesystems.disks.s3.secret')) {
                    $this->storageType = 's3';
                    $this->command->info("[SETTING] Đã thiết lập lưu trữ S3");
                } else {
                    $this->command->warn("Cấu hình S3 không đầy đủ. Sử dụng lưu trữ local thay thế.");
                    $this->storageType = 'public';
                    $this->ensureStorageDirectoryExists();
                }
            } catch (\Exception $e) {
                $this->command->warn("Lỗi khi kiểm tra cấu hình S3: " . $e->getMessage() . ". Sử dụng lưu trữ local thay thế.");
                $this->storageType = 'public';
                $this->ensureStorageDirectoryExists();
            }
        } else if ($storageType === 'sftp') {
            // Kiểm tra xem SFTP disk có khả dụng không
            try {
                if (config('filesystems.disks.sftp.host') && config('filesystems.disks.sftp.username')) {
                    $this->storageType = 'sftp';
                    $this->command->info("[SETTING] Đã thiết lập lưu trữ SFTP");
                } else {
                    $this->command->warn("Cấu hình SFTP không đầy đủ. Sử dụng lưu trữ local thay thế.");
                    $this->storageType = 'public';
                    $this->ensureStorageDirectoryExists();
                }
            } catch (\Exception $e) {
                $this->command->warn("Lỗi khi kiểm tra cấu hình SFTP: " . $e->getMessage() . ". Sử dụng lưu trữ local thay thế.");
                $this->storageType = 'public';
                $this->ensureStorageDirectoryExists();
            }
        } else if ($storageType === 'hotlink') {
            $this->storageType = 'hotlink';
            $this->command->info("[SETTING] Đã thiết lập chế độ hotlink - sử dụng URL ảnh gốc");
        } else {
            $this->command->warn("Kiểu lưu trữ không hợp lệ: $storageType. Sử dụng lưu trữ local.");
            $this->storageType = 'public';
            $this->ensureStorageDirectoryExists();
        }

        return $this;
    }

    /**
     * Đảm bảo thư mục lưu trữ ảnh tồn tại
     */
    protected function ensureStorageDirectoryExists()
    {
        try {
            $baseDir = 'dcn';
            if (!Storage::disk('public')->exists($baseDir)) {
                Storage::disk('public')->makeDirectory($baseDir);
            }

            // Đảm bảo symbolic link tồn tại từ public đến storage
            if (!file_exists(public_path('storage'))) {
                $this->command->info("Tạo symbolic link từ public đến storage");
                // Sử dụng PHP để tạo symbolic link thay vì gọi Artisan
                try {
                    symlink(storage_path('app/public'), public_path('storage'));
                } catch (\Exception $e) {
                    $this->command->error("Không thể tạo symbolic link: " . $e->getMessage());
                    $this->command->info("Hãy chạy lệnh 'php artisan storage:link' để tạo symbolic link");
                }
            }
        } catch (\Exception $e) {
            $this->command->error("Không thể tạo thư mục lưu trữ: " . $e->getMessage());
        }
    }

    /**
     * Upload file với permission phù hợp cho từng storage type
     */
    protected function uploadFileWithPermissions($path, $content, $storageType)
    {
        if ($storageType === 'sftp') {
            // Với SFTP, sử dụng visibility 'public' sẽ áp dụng permPublic = 0755
            return Storage::disk($storageType)->put($path, $content, 'public');
        } else {
            // Với các storage khác, sử dụng visibility 'public' bình thường
            return Storage::disk($storageType)->put($path, $content, 'public');
        }
    }

    /**
     * Khởi tạo danh sách User-Agent mặc định
     */
    protected function initDefaultUserAgents()
    {
        $this->userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:95.0) Gecko/20100101 Firefox/95.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15',
        ];
    }

    /**
     * Thiết lập danh sách proxy
     */
    public function setProxies(array $proxies)
    {
        $this->proxies = $proxies;
        return $this;
    }

    /**
     * Thiết lập danh sách User-Agent
     */
    public function setUserAgents(array $userAgents)
    {
        $this->userAgents = $userAgents;
        return $this;
    }

    /**
     * Thiết lập có sử dụng proxy cho tải ảnh hay không
     */
    public function setUseProxyForImages(bool $use)
    {
        $this->useProxyForImages = $use;
        return $this;
    }

    /**
     * Thiết lập đường dẫn file chứa danh sách URL manga lỗi
     */
    public function setFailedMangaUrlsFile(string $path)
    {
        $this->failedMangaUrlsFile = $path;
        // Cập nhật đường dẫn file log thành công tương ứng
        $this->successMangaUrlsFile = dirname($path) . '/success_manga_urls.log';
        return $this;
    }

    /**
     * Thiết lập cấu hình resize ảnh
     * 
     * @param bool $resize Bật/tắt tính năng resize ảnh
     * @param int|null $width Chiều rộng ảnh sau khi resize (null để giữ nguyên cấu hình hiện tại)
     * @return $this
     */
    public function setResizeImages(bool $resize, ?int $width = null)
    {
        $this->resizeImages = $resize;
        
        if ($width !== null) {
            $this->resizeWidth = $width;
        }
        
        $status = $resize ? 'BẬT' : 'TẮT';
        $this->command->info("[SETTING] {$status} resize ảnh" . ($width ? " (width: {$width}px)" : ""));
        
        return $this;
    }
    
    /**
     * Thiết lập cấu hình nén ảnh
     * 
     * @param bool $compress Bật/tắt tính năng nén ảnh
     * @param int|null $quality Chất lượng ảnh sau khi nén (1-100, null để giữ nguyên cấu hình hiện tại)
     * @return $this
     */
    public function setCompressImages(bool $compress, ?int $quality = null)
    {
        $this->compressImages = $compress;
        
        if ($quality !== null) {
            $this->compressQuality = max(1, min(100, $quality)); // Đảm bảo giá trị từ 1-100
        }
        
        $status = $compress ? 'BẬT' : 'TẮT';
        $this->command->info("[SETTING] {$status} nén ảnh" . ($quality ? " (chất lượng: {$this->compressQuality}%)" : ""));
        
        return $this;
    }

    /**
     * Tạo đối tượng DomCrawler từ chuỗi HTML
     * 
     * @param string $html Chuỗi HTML cần phân tích
     * @return \Symfony\Component\DomCrawler\Crawler
     */
    protected function createDomCrawler($html)
    {
        return new \Symfony\Component\DomCrawler\Crawler($html);
    }

    /**
     * Lấy proxy để sử dụng cho request tiếp theo
     */
    protected function getProxy()
    {
        // Nếu không có proxy nào, trả về null
        if (empty($this->proxies)) {
            return null;
        }
        
        // Nếu đã đạt số lượng request tối đa hoặc chưa có proxy hiện tại, chọn proxy mới
        if ($this->requestCount >= $this->maxRequestPerProxy || $this->currentProxy === null) {
            // Lọc bỏ các proxy không hoạt động
            $availableProxies = array_diff($this->proxies, $this->failedProxies);
            
            // Nếu không còn proxy nào khả dụng, reset danh sách proxy không hoạt động và thử lại
            if (empty($availableProxies)) {
                $this->command->warn('Tất cả proxy đều không hoạt động. Reset danh sách proxy không hoạt động.');
                $this->failedProxies = [];
                $availableProxies = $this->proxies;
            }
            
            // Chọn ngẫu nhiên một proxy từ danh sách khả dụng
            $this->currentProxy = $availableProxies[array_rand($availableProxies)];
            $this->requestCount = 0;
            
            // Không hiển thị log mỗi khi đổi proxy mới
            // $this->command->info('Chuyển sang proxy mới: ' . $this->maskProxy($this->currentProxy));
        }
        
        $this->requestCount++;
        return $this->currentProxy;
    }

    /**
     * Lấy User-Agent ngẫu nhiên
     */
    protected function getUserAgent()
    {
        // Nếu chưa có user agent hiện tại hoặc đã đạt đến số lượng request max, chọn một cái mới
        if ($this->currentUserAgent === null || $this->requestCount >= $this->maxRequestPerProxy) {
            $this->currentUserAgent = $this->userAgents[array_rand($this->userAgents)];
            
            // Không hiển thị log mỗi khi đổi user agent
            // $this->command->info('Chuyển sang User-Agent mới: ' . $this->shortenUserAgent($this->currentUserAgent));
        }
        
        return $this->currentUserAgent;
    }

    /**
     * Rút gọn User-Agent khi hiển thị trong log
     */
    protected function shortenUserAgent($userAgent)
    {
        // Hiển thị phiên bản rút gọn của User-Agent trong log
        if (strlen($userAgent) > 50) {
            return substr($userAgent, 0, 47) . '...';
        }
        return $userAgent;
    }

    /**
     * Che giấu thông tin nhạy cảm trong proxy URL khi hiển thị log
     */
    protected function maskProxy($proxy)
    {
        // Che giấu username và password trong proxy URL
        return preg_replace('/(\w+):(\w+)@/', '***:***@', $proxy);
    }

    /**
     * Đánh dấu proxy không hoạt động
     */
    protected function markProxyAsFailed($proxy)
    {
        if (!in_array($proxy, $this->failedProxies)) {
            $this->failedProxies[] = $proxy;
            $this->command->warn('[PROXY] Proxy không hoạt động: ' . $this->maskProxy($proxy));
        }
    }

    /**
     * Tạo HTTP client không sử dụng proxy
     */
    protected function createClientWithoutProxy()
    {
        $userAgent = $this->getUserAgent();
        
        $config = [
            'base_uri' => $this->baseUrl,
            'headers' => [
                'User-Agent' => $userAgent,
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Referer' => $this->baseUrl,
            ],
            'curl' => [
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
            ],
            'timeout' => 30,
            'connect_timeout' => 10,
        ];
        
        // Bỏ log để giảm bớt thông báo
        // $this->command->info('Đang tạo client không sử dụng proxy');
        return new Client($config);
    }

    /**
     * Tạo HTTP client với proxy hiện tại
     */
    protected function createClient()
    {
        $proxy = $this->getProxy();
        $userAgent = $this->getUserAgent();
        
        $config = [
            'base_uri' => $this->baseUrl,
            'headers' => [
                'User-Agent' => $userAgent,
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Referer' => $this->baseUrl,
            ],
            'curl' => [
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
            ],
            'timeout' => 30,
            'connect_timeout' => 10,
        ];
        
        // Thêm proxy nếu có
        if ($proxy) {
            $config['proxy'] = $proxy;
        }
        
        return new Client($config);
    }

    /**
     * Thực hiện HTTP request với xử lý lỗi và retry
     */
    protected function makeRequest($method, $url, $options = [], $maxRetries = 3)
    {
        $retries = 0;
        $lastException = null;
        
        while ($retries < $maxRetries) {
            try {
                // Tạo client mới với proxy mới cho mỗi lần retry
                $client = $this->createClient();
                
                // Thêm delay giữa các request
                if ($retries > 0) {
                    usleep($this->requestDelay * 1000);
                }
                
                return $client->request($method, $url, $options);
            } catch (\Exception $e) {
                $lastException = $e;
                
                // Chỉ in log khi thử lại lần cuối
                if ($retries == $maxRetries - 1) {
                    $this->command->warn("Request lỗi: {$e->getMessage()}");
                }
                
                // Đánh dấu proxy hiện tại là không hoạt động
                if ($this->currentProxy) {
                    $this->markProxyAsFailed($this->currentProxy);
                    $this->currentProxy = null; // Reset để lần sau chọn proxy mới
                }
                
                // Cũng reset user agent để thử một cái mới
                $this->currentUserAgent = null;
                
                $retries++;
            }
        }
        
        $this->command->error("Đã thử {$maxRetries} lần nhưng không thành công: " . ($lastException ? $lastException->getMessage() : 'Unknown error'));
        return null;
    }

    /**
     * Tải các hình ảnh chapter đa luồng với giới hạn số lượng promise đồng thời
     */
    protected function downloadImagesAsync($imageUrls, $mangaId, $chapterId, $mangaSlug, $chapterSlug, $useProxy = true)
    {
        // Nếu sử dụng chế độ hotlink, trả về trực tiếp mảng URL ảnh
        if ($this->storageType === 'hotlink') {
            return $imageUrls;
        }
        
        // Tạo đường dẫn theo slug thay vì ID
        $relativePathSlug = "dcn/{$mangaSlug}/{$chapterSlug}";
        
        // Đảm bảo thư mục tồn tại
        if ($this->storageType === 'public') {
            if (!Storage::disk('public')->exists($relativePathSlug)) {
                Storage::disk('public')->makeDirectory($relativePathSlug);
            }
        } elseif ($this->storageType === 'sftp') {
            if (!Storage::disk('sftp')->exists($relativePathSlug)) {
                Storage::disk('sftp')->makeDirectory($relativePathSlug);
            }
        }
        
        $totalImages = count($imageUrls);
        $this->command->info("\n[CHAPTER] Bắt đầu tải {$totalImages} ảnh cho chapter {$chapterSlug}");
        
        // Số lượng ảnh tải đồng thời tối đa
        $maxConcurrentImages = 20;
        
        // Tạo progress bar cho toàn bộ quá trình
        $progressBar = $this->command->getOutput()->createProgressBar($totalImages);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% - %message%');
        $progressBar->setMessage('Đang tải ảnh...');
        $progressBar->start();
        
        // Chia nhỏ danh sách ảnh thành các batch để xử lý
        $batches = array_chunk($imageUrls, $maxConcurrentImages, true);
        $downloadedImages = [];
        $totalProcessed = 0;
        $startTime = microtime(true);
        
        // Mảng để theo dõi các ảnh đã thử với từng proxy
        $imageRetryCount = [];
        
        foreach ($batches as $batchIndex => $batch) {
            $batchSize = count($batch);
            $progressBar->setMessage(sprintf('Đang tải batch %d/%d...', $batchIndex + 1, count($batches)));
            
            $promises = [];
            $imageIndices = []; // Lưu mapping giữa index trong promise và số ảnh thực tế
            $imageUrlMap = []; // Lưu mapping giữa imageNumber và URL
            
            // Tạo promise cho mỗi ảnh trong batch
            foreach ($batch as $index => $imageUrl) {
                $imageNumber = $index + 1;
                $imageIndices[] = $imageNumber;
                $imageUrlMap[$imageNumber] = $imageUrl;
                
                // Kiểm tra nếu URL ảnh đã được ghi nhận là 404, bỏ qua luôn
                if (in_array($imageUrl, $this->notFoundImageUrls)) {
                    $progressBar->advance();
                    continue;
                }
                
                // Sử dụng client mới cho mỗi ảnh
                $client = $useProxy ? $this->createClient() : $this->createClientWithoutProxy();
                
                $promises[] = $client->getAsync($imageUrl, [
                    'verify' => false,
                    'headers' => [
                        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
                        'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                        'Referer' => $this->baseUrl
                    ],
                    'timeout' => 30,
                ])->then(
                    function ($response) use ($mangaId, $chapterId, $mangaSlug, $chapterSlug, $imageNumber, $imageUrl, $progressBar) {
                        try {
                            $imageData = $response->getBody()->getContents();
                            
                            // Xử lý hình ảnh với các cấu hình resize và nén
                            $image = ImageManagerStatic::make($imageData);
                            
                            // Áp dụng resize nếu được bật
                            if ($this->resizeImages) {
                                $image->resize($this->resizeWidth, null, function ($constraint) {
                                    $constraint->aspectRatio();
                                });
                            }
                            
                            // Áp dụng nén nếu được bật, ngược lại giữ nguyên định dạng và chất lượng
                            if ($this->compressImages) {
                                $image = $image->encode('jpg', $this->compressQuality);
                            }
                            
                            // Đường dẫn lưu trữ theo slug
                            $relativePath = "dcn/{$mangaSlug}/{$chapterSlug}";
                            $fileName = "{$imageNumber}.jpg";
                            $fullPath = "{$relativePath}/{$fileName}";
                            
                            // Đảm bảo thư mục tồn tại khi lưu
                            if ($this->storageType === 'public' && !Storage::disk('public')->exists($relativePath)) {
                                Storage::disk('public')->makeDirectory($relativePath);
                            } elseif ($this->storageType === 'sftp' && !Storage::disk('sftp')->exists($relativePath)) {
                                Storage::disk('sftp')->makeDirectory($relativePath);
                            }
                            
                            if ($this->uploadFileWithPermissions($fullPath, (string) $image, $this->storageType)) {
                                $uploadedUrl = '';
                                
                                // Tạo URL khác nhau tùy theo kiểu lưu trữ
                                if ($this->storageType === 's3') {
                                    $uploadedUrl = config('app.aws_custom_url') . '/' . $fullPath;
                                } elseif ($this->storageType === 'sftp') {
                                    $uploadedUrl = config('filesystems.disks.sftp.url') . '/' . $fullPath;
                                } else {
                                    // URL cho local storage
                                    $uploadedUrl = url('storage/' . $fullPath);
                                }
                                
                                // Cập nhật progress bar thay vì in thông báo
                                $progressBar->advance();
                                
                                return ['number' => $imageNumber, 'url' => $uploadedUrl];
                            } else {
                                $progressBar->advance();
                                return null;
                            }
                        } catch (\Exception $e) {
                            $progressBar->advance();
                            return null;
                        }
                    },
                    function ($exception) use ($imageNumber, $progressBar, $imageUrl) {
                        // Kiểm tra xem có phải là lỗi 404 không
                        if ($this->is404Exception($exception)) {
                            // Lưu URL ảnh 404 để không retry
                            if (!in_array($imageUrl, $this->notFoundImageUrls)) {
                                $this->notFoundImageUrls[] = $imageUrl;
                            }
                        }
                        
                        $progressBar->advance();
                        return null;
                    }
                );
            }
            
            // Đợi tất cả promise trong batch hoàn thành
            $results = Utils::settle($promises)->wait();
            
            // Xử lý kết quả của các promise
            foreach ($results as $index => $result) {
                if ($result['state'] === 'fulfilled' && $result['value'] !== null) {
                    $imageData = $result['value'];
                    if (is_array($imageData) && isset($imageData['number']) && isset($imageData['url'])) {
                        $downloadedImages[$imageData['number']] = $imageData['url'];
                    }
                }
            }
            
            // Cập nhật số lượng ảnh đã xử lý
            $totalProcessed += $batchSize;
            
            // Xác định các ảnh lỗi cần retry
            $failedImages = array_diff($imageIndices, array_keys($downloadedImages));
            
            // Retry với proxy mới (nếu đang dùng proxy và có proxy khác)
            if ($useProxy && !empty($failedImages) && !empty($this->proxies) && count($this->proxies) > 1) {
                // Lọc bỏ các ảnh 404 không cần retry
                $failedImagesToRetry = [];
                
                foreach ($failedImages as $imageNumber) {
                    if (isset($imageUrlMap[$imageNumber]) && !in_array($imageUrlMap[$imageNumber], $this->notFoundImageUrls)) {
                        $failedImagesToRetry[] = $imageNumber;
                    }
                }
                
                if (!empty($failedImagesToRetry)) {
                    $progressBar->setMessage(sprintf('Đang thử lại %d ảnh lỗi với proxy mới...', count($failedImagesToRetry)));
                    
                    foreach ($failedImagesToRetry as $imageNumber) {
                        // Khởi tạo số lần retry nếu chưa có
                        if (!isset($imageRetryCount[$imageNumber])) {
                            $imageRetryCount[$imageNumber] = 0;
                        }
                        
                        // Kiểm tra xem đã vượt quá số lần retry tối đa chưa
                        if ($imageRetryCount[$imageNumber] >= $this->maxProxyRetries) {
                            continue;
                        }
                        
                        $imageIndex = $imageNumber - 1;
                        $imageUrl = $imageUrls[$imageIndex] ?? null;
                        
                        if ($imageUrl && !in_array($imageUrl, $this->notFoundImageUrls)) {
                            // Tăng số lần retry
                            $imageRetryCount[$imageNumber]++;
                            
                            // Đánh dấu proxy hiện tại là không hoạt động và lấy proxy mới
                            if ($this->currentProxy) {
                                $this->markProxyAsFailed($this->currentProxy);
                                $this->currentProxy = null;
                            }
                            
                            // Tạo client mới với proxy mới
                            $client = $this->createClient();
                            
                            try {
                                $response = $client->request('GET', $imageUrl, [
                                    'verify' => false,
                                    'headers' => [
                                        'User-Agent' => $this->getUserAgent(),
                                        'Accept' => 'image/webp,image/apng,image/*,*/*;q=0.8',
                                        'Referer' => $this->baseUrl
                                    ],
                                    'timeout' => 30,
                                ]);
                                
                                $imageData = $response->getBody()->getContents();
                                
                                // Xử lý hình ảnh với các cấu hình resize và nén
                                $image = ImageManagerStatic::make($imageData);
                                
                                // Áp dụng resize nếu được bật
                                if ($this->resizeImages) {
                                    $image->resize($this->resizeWidth, null, function ($constraint) {
                                        $constraint->aspectRatio();
                                    });
                                }
                                
                                // Áp dụng nén nếu được bật
                                if ($this->compressImages) {
                                    $image = $image->encode('jpg', $this->compressQuality);
                                }
                                
                                $relativePath = "dcn/{$mangaSlug}/{$chapterSlug}";
                                $fileName = "{$imageNumber}.jpg";
                                $fullPath = "{$relativePath}/{$fileName}";
                                
                                if ($this->storageType === 'public' && !Storage::disk('public')->exists($relativePath)) {
                                    Storage::disk('public')->makeDirectory($relativePath);
                                } elseif ($this->storageType === 'sftp' && !Storage::disk('sftp')->exists($relativePath)) {
                                    Storage::disk('sftp')->makeDirectory($relativePath);
                                }
                                
                                if ($this->uploadFileWithPermissions($fullPath, (string) $image, $this->storageType)) {
                                    $uploadedUrl = '';
                                    
                                    if ($this->storageType === 's3') {
                                        $uploadedUrl = config('app.aws_custom_url') . '/' . $fullPath;
                                    } elseif ($this->storageType === 'sftp') {
                                        $uploadedUrl = config('filesystems.disks.sftp.url') . '/' . $fullPath;
                                    } else {
                                        $uploadedUrl = url('storage/' . $fullPath);
                                    }
                                    
                                    $downloadedImages[$imageNumber] = $uploadedUrl;
                                }
                            } catch (\Exception $e) {
                                // Kiểm tra lỗi 404
                                if ($this->is404Exception($e)) {
                                    if (!in_array($imageUrl, $this->notFoundImageUrls)) {
                                        $this->notFoundImageUrls[] = $imageUrl;
                                    }
                                }
                                // Không in log lỗi để tránh rối console
                            }
                        }
                    }
                }
            }
            
            // Cập nhật lại danh sách ảnh lỗi sau khi retry với proxy mới
            $failedImages = array_diff($imageIndices, array_keys($downloadedImages));
            
            // Thử lại các ảnh lỗi không dùng proxy (nếu đang dùng proxy)
            if ($useProxy && !empty($failedImages) && count($downloadedImages) >= count($imageUrls) * 0.3) {
                // Lọc bỏ các ảnh 404 không cần retry
                $failedImagesToRetry = [];
                
                foreach ($failedImages as $imageNumber) {
                    if (isset($imageUrlMap[$imageNumber]) && !in_array($imageUrlMap[$imageNumber], $this->notFoundImageUrls)) {
                        $failedImagesToRetry[] = $imageNumber;
                    }
                }
                
                if (!empty($failedImagesToRetry)) {
                    $progressBar->setMessage(sprintf('Đang thử lại %d ảnh lỗi không dùng proxy...', count($failedImagesToRetry)));
                    
                    $retryPromises = [];
                    foreach ($failedImagesToRetry as $imageNumber) {
                        $imageIndex = $imageNumber - 1;
                        if (isset($imageUrls[$imageIndex])) {
                            $imageUrl = $imageUrls[$imageIndex];
                            
                            if (!in_array($imageUrl, $this->notFoundImageUrls)) {
                                $client = $this->createClientWithoutProxy();
                                $retryPromises[] = $client->getAsync($imageUrl, [
                                    'verify' => false,
                                    'headers' => [
                                        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
                                        'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                                        'Referer' => $this->baseUrl
                                    ],
                                    'timeout' => 30,
                                ])->then(
                                    function ($response) use ($mangaId, $chapterId, $mangaSlug, $chapterSlug, $imageNumber, $imageUrl) {
                                        try {
                                            $imageData = $response->getBody()->getContents();
                                            
                                            // Xử lý hình ảnh với các cấu hình resize và nén
                                            $image = ImageManagerStatic::make($imageData);
                                            
                                            // Áp dụng resize nếu được bật
                                            if ($this->resizeImages) {
                                                $image->resize($this->resizeWidth, null, function ($constraint) {
                                                    $constraint->aspectRatio();
                                                });
                                            }
                                            
                                            // Áp dụng nén nếu được bật, ngược lại giữ nguyên định dạng và chất lượng
                                            if ($this->compressImages) {
                                                $image = $image->encode('jpg', $this->compressQuality);
                                            }
                                            
                                            $relativePath = "dcn/{$mangaSlug}/{$chapterSlug}";
                                            $fileName = "{$imageNumber}.jpg";
                                            $fullPath = "{$relativePath}/{$fileName}";
                                            
                                            if ($this->storageType === 'public' && !Storage::disk('public')->exists($relativePath)) {
                                                Storage::disk('public')->makeDirectory($relativePath);
                                            } elseif ($this->storageType === 'sftp' && !Storage::disk('sftp')->exists($relativePath)) {
                                                Storage::disk('sftp')->makeDirectory($relativePath);
                                            }
                                            
                                            if ($this->uploadFileWithPermissions($fullPath, (string) $image, $this->storageType)) {
                                                $uploadedUrl = '';
                                                
                                                if ($this->storageType === 's3') {
                                                    $uploadedUrl = config('app.aws_custom_url') . '/' . $fullPath;
                                                } elseif ($this->storageType === 'sftp') {
                                                    $uploadedUrl = config('filesystems.disks.sftp.url') . '/' . $fullPath;
                                                } else {
                                                    $uploadedUrl = url('storage/' . $fullPath);
                                                }
                                                
                                                return ['number' => $imageNumber, 'url' => $uploadedUrl];
                                            }
                                        } catch (\Exception $e) {
                                            // Bỏ qua lỗi, không in ra log
                                        }
                                        return null;
                                    },
                                    function ($exception) use ($imageNumber, $imageUrl) {
                                        // Kiểm tra lỗi 404
                                        if ($this->is404Exception($exception)) {
                                            if (!in_array($imageUrl, $this->notFoundImageUrls)) {
                                                $this->notFoundImageUrls[] = $imageUrl;
                                            }
                                        }
                                        return null;
                                    }
                                );
                            }
                        }
                    }
                    
                    if (!empty($retryPromises)) {
                        $retryResults = Utils::settle($retryPromises)->wait();
                        
                        foreach ($retryResults as $result) {
                            if ($result['state'] === 'fulfilled' && $result['value'] !== null) {
                                $imageData = $result['value'];
                                if (is_array($imageData) && isset($imageData['number']) && isset($imageData['url'])) {
                                    $downloadedImages[$imageData['number']] = $imageData['url'];
                                }
                            }
                        }
                    }
                }
            }
            
            // Thêm delay nhỏ giữa các batch để giảm tải cho server nguồn
            if ($batchIndex < count($batches) - 1) {
                usleep(200000); // 200ms delay
            }
        }
        
        // Hoàn thành progress bar
        $totalTime = microtime(true) - $startTime;
        $progressBar->setMessage(sprintf('Hoàn thành (%.1fs)', $totalTime));
        $progressBar->finish();
        $this->command->newLine(2); // Thêm dòng trống để tách biệt
        
        // Sắp xếp ảnh theo số thứ tự
        ksort($downloadedImages);
        $downloadedImages = array_values($downloadedImages);
        
        if (empty($downloadedImages)) {
            $this->command->error("[CHAPTER] Không thành công: " . $chapterSlug);
        } else {
            $this->command->info("[CHAPTER] Đã tải thành công " . count($downloadedImages) . "/" . $totalImages . " ảnh cho chapter {$chapterSlug}");
        }
        
        return $downloadedImages;
    }

    /**
     * Crawl manga từ URL cụ thể
     */
    public function crawlFromUrl($url, $storageType = 'public')
    {
        $this->setStorageType($storageType);
        try {
            $response = $this->makeRequest('GET', $url);
            if (!$response) {
                $this->command->error("Không thể tải thông tin manga từ URL: $url");
                // Lưu URL manga lỗi để có thể retry sau
                $this->logFailedMangaUrl($url);
                return;
            }
            
            $html = (string) $response->getBody();
            
            $mangaDetails = $this->extractMangaDetails($html);
            // Thêm tham chiếu đến crawler vào mangaDetails
            $mangaDetails['crawler'] = $this;
            
            $manga = $this->processor->updateOrCreateManga($mangaDetails);
            $this->processor->attachGenres($manga, $mangaDetails['genres']);
    
            // Sắp xếp chapter theo số trước khi xử lý
            usort($mangaDetails['chapters'], function($a, $b) {
                $numA = $this->processor->extractChapterNumber($a['name']);
                $numB = $this->processor->extractChapterNumber($b['name']);
                return $numA <=> $numB; // Sắp xếp tăng dần
            });
            
            $maxOrder = 0;
            $lastChapterId = null;
            $hasUpdatedChapter = false;
            $totalChapters = count($mangaDetails['chapters']);
            
            $this->command->info("[CHAPTER] {$totalChapters} chapter cho manga: {$manga->name}");
            
            // Tạo progress bar cho xử lý các chapter
            $chapterBar = $this->command->getOutput()->createProgressBar($totalChapters);
            $chapterBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% - %message%');
            $chapterBar->setMessage('Chuẩn bị xử lý...');
            $chapterBar->start();
            
            $processedChapters = 0;
            
            // Xử lý từng chapter một thay vì dùng promise cho tất cả
            foreach ($mangaDetails['chapters'] as $chapter) {
                $processedChapters++;
                
                // Cập nhật thông tin trong progress bar
                $chapterBar->setMessage("Đang xử lý: " . $chapter['name']);
                
                // Kiểm tra xem chapter này đã tồn tại chưa
                $chapterNumber = $this->processor->extractChapterNumber($chapter['name']);
                $chapterNameToSave = ($chapter['name'] === 'Oneshot') ? 'Oneshot' : 'Chapter ' . $chapterNumber;
                $chapterSlug = Str::slug($chapterNameToSave, '-', 'en');
                
                // Thêm thông tin slug vào chapter
                $chapter['slug'] = $chapterSlug;
                
                // Kiểm tra chapter đã tồn tại hay chưa
                $existingChapter = $this->checkChapterExists($manga, $chapter);
                if ($existingChapter) {
                    $chapterBar->advance();
                    continue;
                }
                
                try {
                    // Sử dụng request đồng bộ thay vì promise
                    $client = $this->createClient();
                    $chapterResponse = $client->request('GET', $chapter['link']);
                    $chapterHtml = (string) $chapterResponse->getBody();
                    
                    // Trích xuất thông tin chapter
                    $chapterData = $this->extractChapterDetails($chapterHtml, $chapter);
                    
                    // Thêm thông tin slug từ chapter nếu không có
                    if (!isset($chapterData['slug']) && isset($chapter['slug'])) {
                        $chapterData['slug'] = $chapter['slug'];
                    }
                    
                    // Tải hình ảnh chapter
                    $imageUrls = $chapterData['images'];
                    $chapterId = \Ramsey\Uuid\Uuid::uuid4()->toString();
                    
                    if (empty($imageUrls)) {
                        $chapterBar->advance();
                        continue;
                    }
                    
                    $downloadedImages = $this->downloadImagesAsync($imageUrls, $manga->id, $chapterId, $manga->slug, $chapterData['slug'], $this->useProxyForImages);
                    
                    // Nếu tải thất bại và retryWithoutProxy = true, thử lại không dùng proxy
                    if (($this->useProxyForImages && $this->retryWithoutProxy) && 
                        (empty($downloadedImages) || count($downloadedImages) < count($imageUrls) * 0.5)) {
                        $this->command->warn("[Chapter] Không đủ ảnh tải được. Thử lại không dùng proxy...");
                        $downloadedImages = $this->downloadImagesAsync($imageUrls, $manga->id, $chapterId, $manga->slug, $chapterData['slug'], false);
                    }
                    
                    if (!empty($downloadedImages)) {
                        $chapterData['images'] = $downloadedImages;
                        $chapterData['id'] = $chapterId;
                        $dbChapter = $this->processor->updateOrCreateChapter($manga, $chapterData);
                
                        if ($dbChapter) {
                            $hasUpdatedChapter = true;
                
                            if ($dbChapter->order > $maxOrder) {
                                $maxOrder = $dbChapter->order;
                                $lastChapterId = $dbChapter->id;
                            }
                        }
                    }
                    
                } catch (\Exception $e) {
                    $this->command->error("[Chapter] Xử lý chapter thất bại: " . $chapter['name'] . " - Lỗi: " . $e->getMessage());
                }
                
                $chapterBar->advance();
                
                // Thêm delay nhỏ giữa các chapter để giảm tải cho server nguồn
                usleep(200000); // 200ms delay
            }
            
            $chapterBar->setMessage("Hoàn thành xử lý tất cả chapter");
            $chapterBar->finish();
            $this->command->newLine(2);
    
            if ($hasUpdatedChapter || (!$manga->last_chapter_id && $lastChapterId)) {
                $manga->last_chapter_id = $lastChapterId;
                $manga->save();
                // Chỉ xóa cache một lần sau khi hoàn thành tất cả chapter
                $this->processor->invalidateCache($manga);
            }
    
            $this->command->info("Hoàn thành crawl manga từ URL: {$url}");
            
        } catch (\Exception $e) {
            $this->command->error('Request failed: ' . $e->getMessage());
            // Lưu URL manga lỗi để có thể retry sau
            $this->logFailedMangaUrl($url);
        }
    }

    /**
     * Crawl danh sách manga từ trang
     */
    public function crawlFromPage($startPage, $endPage, $storageType = 'public')
    {
        $this->setStorageType($storageType);
        
        // Tính toán số trang cần crawl
        $pageCount = abs($endPage - $startPage) + 1;
        $this->command->info("[CRAWL] Bắt đầu crawl {$pageCount} trang từ nguồn: {$this->baseUrl}");
        
        // Tạo progress bar cho các trang
        $pageBar = $this->command->getOutput()->createProgressBar($pageCount);
        $pageBar->setFormat(' Trang %current%/%max% [%bar%] %percent:3s%% - %message%');
        $pageBar->setMessage("Chuẩn bị...");
        $pageBar->start();
        
        // Crawl tăng dần hay giảm dần tùy thuộc vào giá trị trang
        if ($startPage <= $endPage) {
            // Crawl tăng dần từ startPage đến endPage
            for ($page = $startPage; $page <= $endPage; $page++) {
                $pageBar->setMessage("Đang xử lý trang {$page}");
                $this->processMangaPage($page);
                $pageBar->advance();
            }
        } else {
            // Crawl giảm dần từ startPage đến endPage
            for ($page = $startPage; $page >= $endPage; $page--) {
                $pageBar->setMessage("Đang xử lý trang {$page}");
                $this->processMangaPage($page);
                $pageBar->advance();
            }
        }

        $pageBar->setMessage("Hoàn thành");
        $pageBar->finish();
        $this->command->newLine(2);
        
        $this->command->info('Đã hoàn thành crawl manga.');
    }

    /**
     * Xử lý trang danh sách manga
     */
    protected function processMangaPage($page)
    {
        $mangaListUrl = $this->getMangaListUrl($page);
        
        $response = $this->makeRequest('GET', $mangaListUrl);
        if (!$response) {
            $this->command->error("Không thể tải trang danh sách manga: $page");
            return;
        }
        
        $mangaListHtml = (string) $response->getBody();
        $mangaData = $this->extractMangaData($mangaListHtml);
        
        if (empty($mangaData)) {
            $this->command->warn("Không tìm thấy manga nào trên trang $page");
            return;
        }

        $this->command->info("Tìm thấy " . count($mangaData) . " manga trên trang $page");
        
        // Tạo progress bar cho việc xử lý manga trên trang hiện tại
        $mangaBar = $this->command->getOutput()->createProgressBar(count($mangaData));
        $mangaBar->setFormat(' Manga %current%/%max% [%bar%] %percent:3s%% - %message%');
        $mangaBar->setMessage("Chuẩn bị...");
        $mangaBar->start();

        foreach ($mangaData as $data) {
            $mangaBar->setMessage("Đang xử lý: " . $data['name']);
            
            $mangaResponse = $this->makeRequest('GET', $data['link']);
            if (!$mangaResponse) {
                // Lưu URL manga lỗi để có thể retry sau
                $this->logFailedMangaUrl($data['link']);
                $mangaBar->advance();
                continue;
            }
            
            $mangaHtml = (string) $mangaResponse->getBody();
            
            try {
                $mangaDetails = $this->extractMangaDetails($mangaHtml);
                // Thêm tham chiếu đến crawler vào mangaDetails
                $mangaDetails['crawler'] = $this;
        
                $manga = $this->processor->updateOrCreateManga($mangaDetails);
                $this->processor->attachGenres($manga, $mangaDetails['genres']);
        
                // Sắp xếp chapter theo số trước khi xử lý
                usort($mangaDetails['chapters'], function($a, $b) {
                    $numA = $this->processor->extractChapterNumber($a['name']);
                    $numB = $this->processor->extractChapterNumber($b['name']);
                    return $numA <=> $numB; // Sắp xếp tăng dần
                });
                
                $maxOrder = 0;
                $lastChapterId = null;
                $hasUpdatedChapter = false;
                $totalChapters = count($mangaDetails['chapters']);
                
                if ($totalChapters > 0) {
                    // Tạo progress bar cho việc xử lý các chapter của manga hiện tại
                    $chapterBar = $this->command->getOutput()->createProgressBar($totalChapters);
                    $chapterBar->setFormat(' Chapter %current%/%max% [%bar%] %percent:3s%% - %message%');
                    $chapterBar->setMessage("Chuẩn bị...");
                    $chapterBar->start();
                    
                    $processedChapters = 0;
                    
                    // Xử lý từng chapter một thay vì dùng promise cho tất cả
                    foreach ($mangaDetails['chapters'] as $chapter) {
                        $processedChapters++;
                        $chapterBar->setMessage($chapter['name']);
                        
                        // Kiểm tra xem chapter này đã tồn tại chưa
                        $chapterNumber = $this->processor->extractChapterNumber($chapter['name']);
                        $chapterNameToSave = ($chapter['name'] === 'Oneshot') ? 'Oneshot' : 'Chapter ' . $chapterNumber;
                        $chapterSlug = Str::slug($chapterNameToSave, '-', 'en');
                        
                        // Thêm thông tin slug vào chapter
                        $chapter['slug'] = $chapterSlug;
                        
                        // Kiểm tra chapter đã tồn tại hay chưa
                        $existingChapter = $this->checkChapterExists($manga, $chapter);
                        if ($existingChapter) {
                            $chapterBar->advance();
                            continue;
                        }
                        
                        try {
                            // Sử dụng request đồng bộ thay vì promise
                            $client = $this->createClient();
                            $chapterResponse = $client->request('GET', $chapter['link']);
                            $chapterHtml = (string) $chapterResponse->getBody();
                            
                            // Trích xuất thông tin chapter
                            $chapterData = $this->extractChapterDetails($chapterHtml, $chapter);
                            
                            // Thêm thông tin slug từ chapter nếu không có
                            if (!isset($chapterData['slug']) && isset($chapter['slug'])) {
                                $chapterData['slug'] = $chapter['slug'];
                            }
                            
                            // Tải hình ảnh chapter
                            $imageUrls = $chapterData['images'];
                            $chapterId = \Ramsey\Uuid\Uuid::uuid4()->toString();
                            
                            if (empty($imageUrls)) {
                                $chapterBar->advance();
                                continue;
                            }
                            
                            $downloadedImages = $this->downloadImagesAsync($imageUrls, $manga->id, $chapterId, $manga->slug, $chapterData['slug'], $this->useProxyForImages);
                            
                            // Nếu tải thất bại và retryWithoutProxy = true, thử lại không dùng proxy
                            if (($this->useProxyForImages && $this->retryWithoutProxy) && 
                                (empty($downloadedImages) || count($downloadedImages) < count($imageUrls) * 0.5)) {
                                $this->command->warn("[Chapter] Không đủ ảnh tải được. Thử lại không dùng proxy...");
                                $downloadedImages = $this->downloadImagesAsync($imageUrls, $manga->id, $chapterId, $manga->slug, $chapterData['slug'], false);
                            }
                            
                            if (!empty($downloadedImages)) {
                                $chapterData['images'] = $downloadedImages;
                                $chapterData['id'] = $chapterId;
                                $dbChapter = $this->processor->updateOrCreateChapter($manga, $chapterData);
                        
                                if ($dbChapter) {
                                    $hasUpdatedChapter = true;
                        
                                    if ($dbChapter->order > $maxOrder) {
                                        $maxOrder = $dbChapter->order;
                                        $lastChapterId = $dbChapter->id;
                                    }
                                }
                            }
                            
                        } catch (\Exception $e) {
                            $this->command->error("[Chapter] Lỗi xử lý chapter: " . $chapter['name'] . " - " . $e->getMessage());
                        }
                        
                        $chapterBar->advance();
                        
                        // Thêm delay nhỏ giữa các chapter để giảm tải cho server nguồn
                        usleep(200000); // 200ms delay
                    }
                    
                    $chapterBar->setMessage("Hoàn thành xử lý tất cả chapter");
                    $chapterBar->finish();
                    $this->command->newLine(2);
                }

                if ($hasUpdatedChapter || (!$manga->last_chapter_id && $lastChapterId)) {
                    $manga->last_chapter_id = $lastChapterId;
                    $manga->save();
                    // Chỉ xóa cache một lần sau khi hoàn thành tất cả chapter
                    $this->processor->invalidateCache($manga);
                }
                
            } catch (\Exception $e) {
                $this->command->error("Lỗi xử lý manga {$data['name']}: " . $e->getMessage());
                // Lưu URL manga lỗi để có thể retry sau
                $this->logFailedMangaUrl($data['link']);
            }
            
            $mangaBar->advance();
        }
        
        $mangaBar->finish();
        $this->command->newLine(2);
    }

    /**
     * Trả về URL cho trang danh sách manga
     */
    abstract protected function getMangaListUrl($page);

    /**
     * Trích xuất dữ liệu manga từ HTML trang danh sách
     */
    abstract protected function extractMangaData($html);

    /**
     * Trích xuất thông tin chi tiết manga từ HTML trang manga
     */
    abstract protected function extractMangaDetails($html);

    /**
     * Trích xuất thông tin chi tiết chapter từ HTML trang chapter
     */
    abstract protected function extractChapterDetails($html, $chapterInfo);

    /**
     * Tải và lưu ảnh bìa manga
     */
    public function downloadAndSaveCover($coverUrl, $mangaId, $mangaSlug = null)
    {
        // Nếu sử dụng chế độ hotlink, trả về trực tiếp URL ảnh
        if ($this->storageType === 'hotlink') {
            $this->command->info("\n[COVER] Sử dụng URL gốc: {$coverUrl}");
            return 'hotlink:' . $coverUrl;
        }
        
        try {
            // Sử dụng client với proxy hoặc không tùy theo cấu hình
            $client = $this->useProxyForImages ? $this->createClient() : $this->createClientWithoutProxy();
            
            $response = $client->request('GET', $coverUrl, [
                'verify' => false,
                'headers' => [
                    'User-Agent' => $this->getUserAgent(),
                    'Accept' => 'image/webp,image/apng,image/*,*/*;q=0.8',
                    'Referer' => $this->baseUrl
                ],
            ]);
            
            $coverData = $response->getBody()->getContents();
            
            // Xử lý ảnh bìa với các cấu hình resize và nén
            $image = ImageManagerStatic::make($coverData);
            
            // Áp dụng resize cố định cho ảnh bìa (400px)
            $image->resize(400, null, function ($constraint) {
                $constraint->aspectRatio();
            });
            
            // Áp dụng nén nếu được bật
            if ($this->compressImages) {
                $image = $image->encode('jpg', $this->compressQuality);
            } else {
                $image = $image->encode('jpg', 90);
            }
            
            // Sử dụng slug nếu có
            if ($mangaSlug) {
                $coverPath = "public/images/covers/{$mangaSlug}.jpg";
            } else {
                $coverPath = "public/images/covers/{$mangaId}.jpg";
            }
            
            Storage::put($coverPath, (string) $image);
            
            $this->command->info("[COVER] Lưu ảnh bìa thành công");
            return $coverPath;
        } catch (\Exception $e) {
            $this->command->error("[COVER] Lỗi khi tải ảnh bìa: " . $e->getMessage());
            
            // Thử lại không dùng proxy nếu đang sử dụng proxy và cấu hình cho phép
            if ($this->useProxyForImages && $this->retryWithoutProxy) {
                $this->command->warn("[COVER] Thử lại tải ảnh bìa không sử dụng proxy...");
                
                try {
                    $client = $this->createClientWithoutProxy();
                    $response = $client->request('GET', $coverUrl, [
                        'verify' => false,
                        'headers' => [
                            'User-Agent' => $this->getUserAgent(),
                            'Accept' => 'image/webp,image/apng,image/*,*/*;q=0.8',
                            'Referer' => $this->baseUrl
                        ],
                    ]);
                    
                    $coverData = $response->getBody()->getContents();
                    
                    // Xử lý ảnh bìa với các cấu hình resize và nén
                    $image = ImageManagerStatic::make($coverData);
                    
                    // Áp dụng resize cố định cho ảnh bìa (400px)
                    $image->resize(400, null, function ($constraint) {
                        $constraint->aspectRatio();
                    });
                    
                    // Áp dụng nén nếu được bật
                    if ($this->compressImages) {
                        $image = $image->encode('jpg', $this->compressQuality);
                    } else {
                        $image = $image->encode('jpg', 90);
                    }
                    
                    // Sử dụng slug nếu có
                    if ($mangaSlug) {
                        $coverPath = "public/images/covers/{$mangaSlug}.jpg";
                    } else {
                        $coverPath = "public/images/covers/{$mangaId}.jpg";
                    }
                    
                    Storage::put($coverPath, (string) $image);
                    
                    $this->command->info("[COVER] Lưu ảnh bìa thành công (không sử dụng proxy)");
                    return $coverPath;
                } catch (\Exception $e2) {
                    $this->command->error("[COVER] Lỗi khi tải ảnh bìa (không sử dụng proxy): " . $e2->getMessage());
                }
            }
            
            return false;
        }
    }

    /**
     * Kiểm tra chapter đã tồn tại trước khi tải
     */
    protected function checkChapterExists($manga, $chapterData)
    {
        // Ủy thác việc kiểm tra cho MangaProcessor để thống nhất logic
        return $this->processor->checkChapterExists($manga, $chapterData);
    }

    /**
     * Ghi URL manga bị lỗi vào file log
     * 
     * @param string $url URL của manga cần lưu
     * @param string $source Nguồn của manga (lxmanga, nettruyenco, ...)
     * @return bool True nếu lưu thành công, False nếu lỗi
     */
    protected function logFailedMangaUrl($url, $source = '')
    {
        try {
            // Đảm bảo thư mục logs tồn tại
            $logDir = dirname($this->failedMangaUrlsFile);
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
            
            // Format: URL|source|timestamp
            $logEntry = $url . '|' . $source . '|' . date('Y-m-d H:i:s') . PHP_EOL;
            
            // Ghi thêm vào cuối file
            file_put_contents($this->failedMangaUrlsFile, $logEntry, FILE_APPEND);
            $this->command->warn("[LOG] Đã lưu URL manga lỗi: {$url}");
            return true;
        } catch (\Exception $e) {
            $this->command->error("[LOG] Không thể lưu URL manga lỗi: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Kiểm tra một URL có phải là 404 (Not Found) hay không
     * 
     * @param \Exception $exception Exception cần kiểm tra
     * @return bool True nếu exception là lỗi 404, False nếu không phải
     */
    protected function is404Exception($exception)
    {
        return (
            $exception instanceof \GuzzleHttp\Exception\ClientException && 
            $exception->getResponse() && 
            $exception->getResponse()->getStatusCode() === 404
        );
    }
} 