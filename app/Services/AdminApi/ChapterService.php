<?php

namespace App\Services\AdminApi;

use App\Helpers\MimeTypeHelper;
use App\Interfaces\Repository\ChapterRepositoryInterface;
use App\Services\BaseCrudService;
use App\Transformers\ChapterTransformer;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ChapterService extends BaseCrudService
{
    public function __construct(ChapterRepositoryInterface $repository, ChapterTransformer $transformer)
    {
        parent::__construct($repository, $transformer);
    }

    public function index()
    {
        $user = auth()->user();

        // If user is translator, only show their own chapters
        if ($user && $user->hasRole('translator')) {
            $conditions = ['user_id' => $user->id];
            return $this->success($this->repository->index($conditions), $this->transformer);
        }

        // Admin can see all chapters
        return parent::index();
    }

    public function show($id)
    {
        $user = auth()->user();
        $chapter = $this->repository->show($id);

        // If user is translator, check if they own this chapter
        if ($user && $user->hasRole('translator') && $chapter->user_id !== $user->id) {
            return $this->error('Unauthorized. You can only access your own resources.', 403);
        }

        return $this->success($chapter, $this->transformer);
    }

    public function store($params)
    {
        // $resource = null;
        try {
            // create resource
            $resource = $this->repository->store(Arr::except($params, ['images']));

            DB::commit();
            return $this->success($resource, $this->transformer);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error("Line {$e->getLine()}: {$e->getMessage()}");
        }
    }

    public function update($id, $params)
    {
        DB::beginTransaction();
        $chapter = $this->repository->getFirstByAttributes(['id' => $id]);
        try {
            $params['content'] = join(PHP_EOL, $params['image_urls']);
            $chapter->update($params);
            $chapter->refresh();

            DB::commit();
            return $this->success($chapter, $this->transformer);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error("Line {$e->getLine()}: {$e->getMessage()}");
        }
    }

    public function updateChapterOrder($data)
    {
        foreach ($data as $item) {
            $this->repository->update($item['id'], ['order' => $item['order']]);
        }
        return $this->success(null, null, 203);
    }

    public function deleteMany($ids)
    {
        $this->repository->model()->whereIn('id', $ids)->delete();
        return $this->success(null, null, 203);
    }

    public function addImage($chapter_id, UploadedFile $image)
    {
        DB::beginTransaction();
        try {
            $chapter = $this->repository->getFirstByAttributes(['id' => $chapter_id]);
            
            // Khởi tạo mảng content
            $content = [];
            
            // Xử lý content hiện tại
            if (!empty($chapter->content)) {
                if (is_array($chapter->content)) {
                    $content = array_values(array_filter($chapter->content));
                } else {
                    // Phân tách chuỗi thành mảng và loại bỏ các phần tử rỗng
                    $content = array_values(array_filter(explode(PHP_EOL, (string)$chapter->content), function($url) {
                        return !empty(trim($url));
                    }));
                }
            }
    
            // Đường dẫn lưu ảnh trên S3
            $s3Path = "images/data/{$chapter->manga_id}/{$chapter->id}/" . count($content) . "." .
                MimeTypeHelper::getMimeExtension($image->getMimeType());
    
            // Đọc nội dung ảnh
            $imageContent = $image->getContent();
    
            // Tạo đối tượng ảnh từ nội dung ảnh
            $img = \Intervention\Image\Facades\Image::make($imageContent);
    
            // Nếu đây là ảnh đầu tiên, thêm banner vào trên cùng
            if (count($content) == 0) {
                $banner = \Intervention\Image\Facades\Image::make(public_path('dcncc.jpg'));
    
                // Resize banner để có cùng chiều rộng với ảnh đầu tiên
                $banner->resize($img->width(), null, function ($constraint) {
                    $constraint->aspectRatio();
                });
    
                // Tạo ảnh mới với chiều cao tổng của banner và ảnh đầu tiên
                $combined = \Intervention\Image\Facades\Image::canvas($img->width(), $img->height() + $banner->height());
    
                // Chèn banner vào trên cùng
                $combined->insert($banner, 'top');
    
                // Chèn ảnh đầu tiên bên dưới banner
                $combined->insert($img, 'bottom');
    
                // Cập nhật đối tượng ảnh hiện tại thành ảnh kết hợp
                $img = $combined;
            }
    
            // Thêm watermark vào ảnh
            $watermark = \Intervention\Image\Facades\Image::make(public_path('dcn.png'))->opacity(50);
            $img->insert($watermark, 'bottom-right', 10, 10);
    
            // Upload ảnh lên S3
            Storage::disk('s3')->put($s3Path, (string) $img->encode(), 'public');
    
            // Tạo URL trực tiếp từ bucket name và path
            $fullUrl = config('app.aws_custom_url') . '/' . $s3Path;
            $content[] = $fullUrl;
    
            // Lưu content với mỗi URL một dòng, đảm bảo không có dòng trống
            $chapter->update(['content' => implode(PHP_EOL, $content)]);
    
            DB::commit();
            return $this->success(null, null, 204);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error("Line {$e->getLine()}: {$e->getMessage()}");
        }
    }

    public function clearImage($chapter_id)
    {
        try {
            $chapter = $this->repository->getFirstByAttributes(['id' => $chapter_id]);
            Storage::disk('s3')->deleteDirectory("images/data/{$chapter->manga_id}/{$chapter->id}");
            $chapter->update(['content' => null]);

            DB::commit();
            return $this->success(null, null, 203);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error("Line {$e->getLine()}: {$e->getMessage()}");
        }
    }
}
