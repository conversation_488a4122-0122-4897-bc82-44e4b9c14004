<?php

namespace App\Services\AdminApi;

use App\Http\Requests\Admin\LoginRequest;
use App\Interfaces\Repository\UserRepositoryInterface;
use App\Traits\HasTransformer;
use Illuminate\Support\Facades\Hash;

class AuthService
{
    use HasTransformer;

    private $userRepository;

    public function __construct(UserRepositoryInterface $repository)
    {
        $this->userRepository = $repository;
    }

    public function login(LoginRequest $request)
    {
        $admin = $this->userRepository->getFirstByAttributes(['email' => $request->input('email')]);
        if ($admin && $admin->hasAnyRole(['admin', 'translator']) && Hash::check($request->input('password'), $admin->password)) {
            $tokenObj = $admin->createToken('admin-token');
            return $this->success(['token' => $tokenObj->plainTextToken, 'type' => 'Bearer']);
        }
        return $this->error('User or password error', 401);
    }

    public function profile()
    {
        $admin = auth()->user();
        return $this->success(array_merge($admin->toArray(), [
            'roles' => $admin->roles()->get()->pluck('name')
        ]));
    }

    public function logout()
    {
        $admin = auth()->user();
        $admin->currentAccessToken()->delete();
        return $this->success(null, null, 204);
    }
}
