<?php

namespace App\Services\AdminApi;

use App\Interfaces\Repository\MangaRepositoryInterface;
use App\Notifications\MangaApproveNotification;
use App\Services\BaseCrudService;
use App\Transformers\MangaTransformer;

class MangaService extends BaseCrudService
{
    public function __construct(MangaRepositoryInterface $repository, MangaTransformer $transformer)
    {
        parent::__construct($repository, $transformer);
    }

    public function index()
    {
        $user = auth()->user();

        // If user is translator, only show their own mangas
        if ($user && $user->hasRole('translator')) {
            $conditions = ['user_id' => $user->id];
            return $this->success($this->repository->index($conditions), $this->transformer);
        }

        // Admin can see all mangas
        return parent::index();
    }

    public function show($id)
    {
        $user = auth()->user();
        $manga = $this->repository->show($id);

        // If user is translator, check if they own this manga
        if ($user && $user->hasRole('translator') && $manga->user_id !== $user->id) {
            return $this->error('Unauthorized. You can only access your own resources.', 403);
        }

        return $this->success($manga, $this->transformer);
    }

    public function store($params)
    {
        $params['is_reviewed'] = true;
        $resource = $this->repository->store($params);

        // sync genres
        $resource->genres()->sync($params['genres'] ?? []);

        if (isset($params['is_hot']) && $params['is_hot']) {
            $resource->update([
                'hot_at' => now()
            ]);
        }

        return $this->success($resource, $this->transformer);
    }

    public function update($id, $params)
    {
        $resource = $this->repository->getFirstByAttributes(['id' => $id]);

        // update
        $resource->timestamps = false;
        $resource->update($params);

        // sync genres
        if (isset($params['genres'])) {
            $resource->genres()->sync($params['genres']);
        }

        if (isset($params['is_hot']) && $params['is_hot']) {
            $resource->update([
                'hot_at' => now()
            ]);
        }

        // If reviewed
        if (isset($params['is_reviewed'])) {
            $notification = new MangaApproveNotification($resource);
            $resource->user->notify($notification);
        }

        return $this->success($resource, $this->transformer);
    }
}
