<?php

namespace App\Http\Middleware;

use App\Models\Manga;
use App\Models\Chapter;
use App\Models\Artist;
use App\Models\Group;
use App\Models\Doujinshi;
use App\Models\Pet;
use App\Models\Achievement;
use Closure;
use Illuminate\Http\Request;

class AdminOrOwner
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $model
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $model = null)
    {
        $user = auth()->user();
        
        // Admin có thể làm tất cả
        if ($user->hasRole('admin')) {
            return $next($request);
        }

        // Translator cần kiểm tra ownership
        if ($user->hasRole('translator')) {
            $resourceId = $request->route('id') ?? $request->route()->parameter('id');
            
            if (!$resourceId) {
                return response()->json(['code' => 400, 'message' => 'Resource ID not found'], 400);
            }

            $isOwner = $this->checkOwnership($model, $resourceId, $user->id);
            
            if ($isOwner) {
                return $next($request);
            }
        }

        return response()->json(['code' => 403, 'message' => 'Access denied. You can only modify your own content.'], 403);
    }

    /**
     * Check if user owns the resource
     *
     * @param string $model
     * @param string $resourceId
     * @param string $userId
     * @return bool
     */
    private function checkOwnership($model, $resourceId, $userId)
    {
        switch ($model) {
            case 'manga':
                return Manga::where('id', $resourceId)->where('user_id', $userId)->exists();
            
            case 'chapter':
                // Check if user owns the chapter directly or owns the manga that contains the chapter
                $chapter = Chapter::find($resourceId);
                if (!$chapter) return false;

                return $chapter->user_id === $userId ||
                       ($chapter->manga && $chapter->manga->user_id === $userId);
            
            case 'artist':
                return Artist::where('id', $resourceId)->where('user_id', $userId)->exists();
            
            case 'group':
                return Group::where('id', $resourceId)->where('user_id', $userId)->exists();
            
            case 'doujinshi':
                return Doujinshi::where('id', $resourceId)->where('user_id', $userId)->exists();

            case 'pet':
                return Pet::where('id', $resourceId)->where('user_id', $userId)->exists();

            case 'achievement':
                return Achievement::where('id', $resourceId)->where('user_id', $userId)->exists();

            default:
                return false;
        }
    }
}
