<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class TranslatorAuth
{
    /**
     * Handle an incoming request.
     * Check if user is translator and can only access their own resources
     */
    public function handle(Request $request, Closure $next)
    {
        $user = auth()->user();

        // Allow admin to access everything
        if ($user && $user->hasRole('admin')) {
            return $next($request);
        }

        // Check if user is translator
        if (!$user || !$user->hasRole('translator')) {
            return response()->json(['message' => 'Unauthorized. Translator role required.'], 403);
        }

        // For GET requests (index, show), allow access but filter results in controller
        if ($request->isMethod('GET')) {
            return $next($request);
        }

        // For POST, PUT, DELETE requests, check ownership of resources
        $resourceId = $this->getResourceId($request);
        $resourceType = $this->getResourceType($request);

        // Special handling for bulk operations
        if ($this->isBulkOperation($request)) {
            if (!$this->checkBulkOwnership($user, $request)) {
                return response()->json(['message' => 'Unauthorized. You can only access your own resources.'], 403);
            }
        } elseif ($resourceId && $resourceType) {
            if (!$this->checkOwnership($user, $resourceType, $resourceId)) {
                return response()->json(['message' => 'Unauthorized. You can only access your own resources.'], 403);
            }
        }

        return $next($request);
    }
    
    /**
     * Get resource ID from route parameters
     */
    private function getResourceId(Request $request)
    {
        // Check for manga ID
        if ($request->route('manga')) {
            return $request->route('manga');
        }
        
        // Check for chapter ID  
        if ($request->route('chapter')) {
            return $request->route('chapter');
        }
        
        // Check for generic ID parameter
        if ($request->route('id')) {
            return $request->route('id');
        }
        
        return null;
    }
    
    /**
     * Determine resource type from route
     */
    private function getResourceType(Request $request)
    {
        $path = $request->path();
        
        if (strpos($path, 'mangas') !== false) {
            return 'manga';
        }
        
        if (strpos($path, 'chapters') !== false) {
            return 'chapter';
        }
        
        return null;
    }
    
    /**
     * Check if this is a bulk operation
     */
    private function isBulkOperation(Request $request)
    {
        $path = $request->path();
        return strpos($path, 'chapters-order') !== false ||
               strpos($path, 'delete-many') !== false;
    }

    /**
     * Check ownership for bulk operations
     */
    private function checkBulkOwnership($user, Request $request)
    {
        $path = $request->path();

        if (strpos($path, 'chapters-order') !== false) {
            $chaptersOrder = $request->input('chapters_order', []);
            foreach ($chaptersOrder as $chapterData) {
                $chapter = \App\Models\Chapter::find($chapterData['id']);
                if (!$chapter || $chapter->user_id !== $user->id) {
                    return false;
                }
            }
            return true;
        }

        if (strpos($path, 'delete-many') !== false) {
            $ids = $request->input('ids', []);
            foreach ($ids as $id) {
                $chapter = \App\Models\Chapter::find($id);
                if (!$chapter || $chapter->user_id !== $user->id) {
                    return false;
                }
            }
            return true;
        }

        return false;
    }

    /**
     * Check if user owns the resource
     */
    private function checkOwnership($user, $resourceType, $resourceId)
    {
        switch ($resourceType) {
            case 'manga':
                $manga = \App\Models\Manga::find($resourceId);
                return $manga && $manga->user_id === $user->id;

            case 'chapter':
                $chapter = \App\Models\Chapter::find($resourceId);
                return $chapter && $chapter->user_id === $user->id;

            default:
                return false;
        }
    }
}
