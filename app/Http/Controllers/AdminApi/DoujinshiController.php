<?php

namespace App\Http\Controllers\AdminApi;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateDoujinshiRequest;
use App\Http\Requests\Admin\UpdateDoujinshiRequest;
use App\Interfaces\Controller\DoujinshiControllerInterface;
use App\Services\AdminApi\DoujinshiService;

class DoujinshiController extends Controller implements DoujinshiControllerInterface
{
    private DoujinshiService $service;

    public function __construct(DoujinshiService $service)
    {
        $this->middleware(['auth:user'])->except('index', 'show');
        $this->middleware(['adminOrOwner:doujinshi'])->only(['update', 'destroy']);
        $this->service = $service;
    }

    public function index()
    {
        return $this->service->index();
    }

    public function show($id)
    {
        return $this->service->show($id);
    }

    public function store(CreateDoujinshiRequest $request)
    {
        return $this->service->store(array_merge($request->validated(), [
            'user_id' => auth()->id()
        ]));
    }

    public function update($id, UpdateDoujinshiRequest $request)
    {
        return $this->service->update($id, $request->validated());
    }

    public function destroy($id)
    {
        return $this->service->destroy($id);
    }
}
