<?php

namespace App\Http\Controllers\AdminApi;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateChapterRequest;
use App\Http\Requests\Admin\DeleteManyChapterRequest;
use App\Http\Requests\Admin\UpdateChapterImageRequest;
use App\Http\Requests\Admin\UpdateChapterRequest;
use App\Http\Requests\Admin\UpdateChaptersOrderRequest;
use App\Interfaces\Controller\ChapterControllerInterface;
use App\Services\AdminApi\ChapterService;

class ChapterController extends Controller implements ChapterControllerInterface
{
    private ChapterService $service;

    public function __construct(ChapterService $service)
    {
        $this->middleware(['auth:user'])->except('index', 'show');
        $this->middleware(['adminOrOwner:chapter'])->only(['update', 'destroy', 'addImage', 'clearImage']);
        $this->middleware(['adminOnly'])->only(['updateChapterOrder', 'deleteMany']);
        $this->service = $service;
    }

    public function index()
    {
        return $this->service->index();
    }

    public function show($id)
    {
        return $this->service->show($id);
    }

    public function store(CreateChapterRequest $request)
    {
        return $this->service->store(array_merge($request->validated(), [
            'user_id' => auth()->id()
        ]));
    }

    public function update($id, UpdateChapterRequest $request)
    {
        return $this->service->update($id, $request->validated());
    }

    public function updateChapterOrder(UpdateChaptersOrderRequest $request)
    {
        return $this->service->updateChapterOrder($request->input('chapters_order'));
    }

    public function addImage($id, UpdateChapterImageRequest $request)
    {
        return $this->service->addImage($id, $request->file('image'));
    }

    public function deleteMany(DeleteManyChapterRequest $request)
    {
        return $this->service->deleteMany($request->input('ids'));
    }

    public function clearImage($id)
    {
        return $this->service->clearImage($id);
    }

    public function destroy($id)
    {
        return $this->service->destroy($id);
    }
}
