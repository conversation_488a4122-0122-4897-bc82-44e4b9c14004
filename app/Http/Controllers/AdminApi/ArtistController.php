<?php

namespace App\Http\Controllers\AdminApi;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateArtistRequest;
use App\Http\Requests\Admin\UpdateArtistRequest;
use App\Interfaces\Controller\ArtistControllerInterface;
use App\Services\AdminApi\ArtistService;

class ArtistController extends Controller implements ArtistControllerInterface
{
    private ArtistService $service;

    public function __construct(ArtistService $service)
    {
        $this->middleware('auth:user')->except('index', 'show');
        $this->middleware(['adminOrOwner:artist'])->only(['update', 'destroy']);
        $this->service = $service;
    }

    public function index()
    {
        return $this->service->index();
    }

    public function show($id)
    {
        return $this->service->show($id);
    }

    public function store(CreateArtistRequest $request)
    {
        return $this->service->store(array_merge($request->validated(), [
            'user_id' => auth()->id()
        ]));
    }

    public function update($id, UpdateArtistRequest $request)
    {
        return $this->service->update($id, $request->validated());
    }

    public function destroy($id)
    {
        return $this->service->destroy($id);
    }
}
