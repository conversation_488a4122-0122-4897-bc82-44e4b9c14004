<?php

namespace App\Http\Controllers\AdminApi;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateGenreRequest;
use App\Http\Requests\Admin\UpdateGenreRequest;
use App\Interfaces\Controller\GenreControllerInterface;
use App\Services\AdminApi\GenreService;

class GenreController extends Controller implements GenreControllerInterface
{
    private GenreService $service;

    public function __construct(GenreService $service)
    {
        $this->middleware(['auth:user'])->except('index', 'show');
        $this->middleware(['adminOnly'])->only(['store', 'update', 'destroy']);
        $this->service = $service;
    }

    public function index()
    {
        return $this->service->index();
    }

    public function show($id)
    {
        return $this->service->show($id);
    }

    public function store(CreateGenreRequest $request)
    {
        return $this->service->store($request->validated());
    }

    public function update($id, UpdateGenreRequest $request)
    {
        return $this->service->update($id, $request->validated());
    }

    public function destroy($id)
    {
        return $this->service->destroy($id);
    }
}
