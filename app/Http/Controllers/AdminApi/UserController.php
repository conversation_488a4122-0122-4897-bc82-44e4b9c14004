<?php

namespace App\Http\Controllers\AdminApi;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateUserRequest;
use App\Services\AdminApi\UserService;

class UserController extends Controller
{
    private $service;

    public function __construct(UserService $service)
    {
        $this->middleware(['auth:user', 'adminOnly']);
        $this->service = $service;
    }

    public function index()
    {
        return $this->service->index();
    }

    public function show($id)
    {
        return $this->service->show($id);
    }

    public function update($id, UpdateUserRequest $request)
    {
        return $this->service->update($id, $request->validated());
    }

    public function deleteComment($id)
    {
        return $this->service->deleteComment($id);
    }
}
