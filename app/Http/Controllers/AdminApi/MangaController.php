<?php

namespace App\Http\Controllers\AdminApi;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateMangaRequest;
use App\Http\Requests\Admin\UpdateMangaRequest;
use App\Interfaces\Controller\MangaControllerInterface;
use App\Services\AdminApi\MangaService;

class MangaController extends Controller implements MangaControllerInterface
{
    private MangaService $service;

    public function __construct(MangaService $service)
    {
        $this->middleware(['auth:user'])->except('index', 'show');
        $this->middleware(['adminOrOwner:manga'])->only(['update', 'destroy']);
        $this->service = $service;
    }

    public function index()
    {
        return $this->service->index();
    }

    public function show($id)
    {
        return $this->service->show($id);
    }

    public function store(CreateMangaRequest $request)
    {
        return $this->service->store(array_merge($request->validated(), [
            'user_id' => auth()->id()
        ]));
    }

    public function update(UpdateMangaRequest $request, $id)
    {
        return $this->service->update($id, $request->validated());
    }

    public function destroy($id)
    {
        return $this->service->destroy($id);
    }
}
