<?php

namespace App\Http\Controllers\AdminApi;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateAchievementRequest;
use App\Http\Requests\Admin\UpdateAchievementRequest;
use App\Interfaces\Controller\AchievementControllerInterface;
use App\Services\AdminApi\AchievementService;

class AchievementController extends Controller implements AchievementControllerInterface
{
    private AchievementService $service;

    public function __construct(AchievementService $service)
    {
        $this->middleware(['auth:user']);
        $this->middleware(['adminOrOwner:achievement'])->only(['update', 'destroy']);
        $this->service = $service;
    }

    public function index()
    {
        return $this->service->index();
    }

    public function show($id)
    {
        return $this->service->show($id);
    }

    public function store(CreateAchievementRequest $request)
    {
        return $this->service->store(array_merge($request->validated(), [
            'user_id' => auth()->id()
        ]));
    }

    public function update($id, UpdateAchievementRequest $request)
    {
        return $this->service->update($id, $request->validated());
    }

    public function destroy($id)
    {
        return $this->service->destroy($id);
    }
}
