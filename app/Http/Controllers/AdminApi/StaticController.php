<?php

namespace App\Http\Controllers\AdminApi;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateAnnouncementRequest;
use App\Services\AdminApi\StaticService;

class StaticController extends Controller
{
    private $service;

    public function __construct(StaticService $service)
    {
        $this->middleware(['auth:user']);
        $this->middleware(['adminOnly'])->only(['updateAnnouncement']);
        $this->service = $service;
    }

    public function basicStatic()
    {
        return $this->service->basicStatic();
    }

    public function getAnnouncement()
    {
        return $this->service->getAnnouncement();
    }

    public function updateAnnouncement(UpdateAnnouncementRequest $request)
    {
        return $this->service->updateAnnouncement($request->input('html'));
    }
}
