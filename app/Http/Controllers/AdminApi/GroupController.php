<?php

namespace App\Http\Controllers\AdminApi;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateGroupRequest;
use App\Http\Requests\Admin\UpdateGroupRequest;
use App\Interfaces\Controller\GroupControllerInterface;
use App\Services\AdminApi\GroupService;

class GroupController extends Controller implements GroupControllerInterface
{
    private GroupService $service;

    public function __construct(GroupService $service)
    {
        $this->middleware(['auth:user']);
        $this->middleware(['adminOrOwner:group'])->only(['update', 'destroy']);
        $this->service = $service;
    }

    public function index()
    {
        return $this->service->index();
    }

    public function show($id)
    {
        return $this->service->show($id);
    }

    public function store(CreateGroupRequest $request)
    {
        return $this->service->store(array_merge($request->validated(), [
            'user_id' => auth()->id()
        ]));
    }

    public function update($id, UpdateGroupRequest $request)
    {
        return $this->service->update($id, $request->validated());
    }

    public function destroy($id)
    {
        return $this->service->destroy($id);
    }
}
