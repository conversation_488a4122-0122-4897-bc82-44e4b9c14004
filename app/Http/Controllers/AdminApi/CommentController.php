<?php

namespace App\Http\Controllers\AdminApi;

use App\Http\Controllers\Controller;
use App\Interfaces\Controller\CommentControllerInterface;
use App\Services\AdminApi\CommentService;
use Illuminate\Http\Request;

class CommentController extends Controller implements CommentControllerInterface
{
    private CommentService $service;

    public function __construct(CommentService $service)
    {
        $this->middleware(['auth:user'])->except(['index', 'show']);
        $this->middleware(['adminOnly'])->only(['destroy']);
        $this->service = $service;
    }

    public function index()
    {
        return $this->service->index();
    }

    public function show($id)
    {
        return $this->service->show($id);
    }

    public function store(Request $request)
    {
        return $this->service->store($request->input());
    }

    public function update($id, Request $request)
    {
        return $this->service->update($id, $request->input());
    }

    public function destroy($id)
    {
        return $this->service->destroy($id);
    }
}
