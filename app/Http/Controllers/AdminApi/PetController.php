<?php

namespace App\Http\Controllers\AdminApi;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreatePetRequest;
use App\Http\Requests\Admin\UpdatePetRequest;
use App\Interfaces\Controller\PetControllerInterface;
use App\Services\AdminApi\PetService;

class PetController extends Controller implements PetControllerInterface
{
    private PetService $service;

    public function __construct(PetService $service)
    {
        $this->middleware('auth:user');
        $this->middleware(['adminOrOwner:pet'])->only(['update', 'destroy']);
        $this->service = $service;
    }

    public function index()
    {
        return $this->service->index();
    }

    public function show($id)
    {
        return $this->service->show($id);
    }

    public function store(CreatePetRequest $request)
    {
        return $this->service->store(array_merge($request->validated(), [
            'user_id' => auth()->id()
        ]));
    }

    public function update($id, UpdatePetRequest $request)
    {
        return $this->service->update($id, $request->validated());
    }

    public function destroy($id)
    {
        return $this->service->destroy($id);
    }
}
