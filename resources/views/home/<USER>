@extends('layouts.app')
@section('head')
@endsection
@section('title')
    {{ trim($manga->name . (isset($chapter) ? ' Chương ' . $chapter->getChapterNumber() . ' Next Chương ' . ($chapter->getChapterNumber() + 1) : '')) }}
@endsection
@section('head_meta')
    <meta name="description"
        content="Đọc truyện tranh Hentai 18+ {{ $manga->name }} @if (isset($chapter)) Chương {{ $chapter->getChapterNumber() }}@else
        {{ $chapter->name }} @endif Manhwa Hentai tại {{ config('app.name') }}">
    <meta property="og:title"
        content="Đọc truyện tranh Hentai {{ $manga->name }} @if (isset($chapter)) Chương {{ $chapter->getChapterNumber() }} Next Chương {{ $chapter->getChapterNumber() + 1 }}@else
        {{ $chapter->name }} @endif - {{ config('app.name') }}" />
    <meta property="og:url" content="{{ secure_url("truyen/{$manga->slug}/{$chapter->slug}") }}" />
    <meta property="og:site_name" content="{{ config('app.name') }}" />
    <meta property="og:type" content="article" />
    <meta property="og:image" content="{{ $manga->cover_full_url }}">
    <meta property="og:description"
        content="Đọc truyện tranh Hentai {{ $manga->name }} @if (isset($chapter)) Chương {{ $chapter->getChapterNumber() }} Next Chương {{ $chapter->getChapterNumber() + 1 }}@else
        {{ $chapter->name }} @endif - {{ config('app.name') }}">
    <meta itemprop="name"
        content="Đọc truyện tranh Hentai {{ $manga->name }} @if (isset($chapter)) Chương {{ $chapter->getChapterNumber() }} Next Chương {{ $chapter->getChapterNumber() + 1 }}@else
        {{ $chapter->name }} @endif - {{ config('app.name') }}">
    <meta itemprop="description"
        content="Đọc truyện tranh Hentai 18+ {{ $manga->name }} @if (isset($chapter)) Chương {{ $chapter->getChapterNumber() }}@else
        {{ $chapter->name }} @endif Manhwa Hentai tại {{ config('app.name') }}">
    <meta itemprop="image" content="{{ $manga->cover_full_url }}">
    <link rel="preconect" href="{{ config('app.aws_custom_url') }}"/>
    <link rel="dns-prefetch" href="{{ config('app.aws_custom_url') }}"/>
@endsection

@section('body')
    <main>
        <div class="max-w-7xl mx-auto px-3 w-full mt-6" x-data="{ listOpen: false }">
            {{-- Mobile Fixed Navigation Bar --}}
            <div id="mobile-chapter-nav"
                class="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-light-blue border-b border-gray-200 dark:border-gray-700 md:hidden transition-opacity duration-300 opacity-0 -translate-y-full transform">
                <nav class="grid grid-cols-4">
                    <a href="{{ $prev_chapter ? "/truyen/{$manga->slug}/{$prev_chapter['slug']}" : 'javascript:void(0)' }}"
                        class="border-r border-gray-200 dark:border-gray-700">
                        <button
                            class="flex justify-center py-3 cursor-pointer w-full {{ $prev_chapter ? 'hover:bg-gray-200 dark:hover:bg-gray-600' : 'opacity-40 cursor-not-allowed' }}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-arrow-left" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"/>
                            </svg>
                        </button>
                    </a>
                    <a href="/truyen/{{ $manga->slug }}#chapterList"
                        class="border-r border-gray-200 dark:border-gray-700">
                        <button
                            class="flex justify-center py-3 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 w-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-book" viewBox="0 0 16 16">
                                <path d="M1 2.828c.885-.37 2.154-.769 3.388-.893 1.33-.134 2.458.063 3.112.752v9.746c-.935-.53-2.12-.603-3.213-.493-1.18.12-2.37.461-3.287.811zm7.5-.141c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492zM8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 0 0 0 2.5v11a.5.5 0 0 0 .707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 0 0 .78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0 0 16 13.5v-11a.5.5 0 0 0-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783"/>
                              </svg>
                        </button>
                    </a>
                    <button @click="listOpen = !listOpen"
                        class="border-r border-gray-200 dark:border-gray-700 flex justify-center py-3 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 w-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor"
                            class="bi bi-list" viewBox="0 0 16 16">
                            <path fill-rule="evenodd"
                                d="M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5m0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5m0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5" />
                        </svg>
                    </button>
                    <a
                        href="{{ $next_chapter ? "/truyen/{$manga->slug}/{$next_chapter['slug']}" : 'javascript:void(0)' }}">
                        <button
                            class="flex justify-center py-3 cursor-pointer w-full {{ $next_chapter ? 'hover:bg-gray-200 dark:hover:bg-gray-600' : 'opacity-40 cursor-not-allowed' }}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8"/>
                            </svg>
                        </button>
                </nav>
            </div>
            {{-- Breadcrumb --}}
            <div class="flex py-3 px-5 text-gray-700 bg-gray-50 rounded-lg border border-gray-200 dark:bg-light-blue dark:border-gray-700 shadow-md truncate mb-4"
                aria-label="Breadcrumb">
                <ol class="flex flex-wrap items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center" itemprop="itemListElement" itemscope=""
                        itemtype="http://schema.org/ListItem">
                        <a href="{{ secure_url('') }}"
                            class="ml-1 text-sm font-medium text-gray-700 hover:text-gray-900 md:ml-2 dark:text-gray-400 dark:hover:text-white truncate"
                            itemprop="item" itemtype="http://schema.org/Thing">
                            <span itemprop="name">{{ __('Home') }}</span>
                        </a>
                        <meta itemprop="position" content="1">
                    </li>
                    <li aria-current="page" itemprop="itemListElement" itemscope="" itemtype="http://schema.org/ListItem">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd"
                                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                    clip-rule="evenodd"></path>
                            </svg>
                            <a href="{{ secure_url('tim-kiem') }}"
                                class="ml-1 text-sm font-medium text-gray-700 hover:text-gray-900 md:ml-2 dark:text-gray-400 dark:hover:text-white truncate"
                                itemprop="item" itemtype="http://schema.org/Thing">
                                <span itemprop="name">{{ __('Manga') }}</span>
                            </a>
                            <meta itemprop="position" content="2">
                        </div>
                    </li>
                    <li aria-current="page" itemprop="itemListElement" itemscope="" itemtype="http://schema.org/ListItem">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd"
                                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                    clip-rule="evenodd"></path>
                            </svg>
                            <a href="{{ secure_url("truyen/{$manga->slug}") }}"
                                class="ml-1 text-sm font-medium text-gray-700 hover:text-gray-900 md:ml-2 dark:text-gray-400 dark:hover:text-white truncate"
                                itemprop="item" itemtype="http://schema.org/Thing">
                                <span itemprop="name">{{ $manga->name }}</span>
                            </a>
                            <meta itemprop="position" content="3">
                        </div>
                    </li>
                    <li aria-current="page" itemprop="itemListElement" itemscope=""
                        itemtype="http://schema.org/ListItem">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd"
                                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                    clip-rule="evenodd"></path>
                            </svg>
                            <a href="{{ secure_url("truyen/{$manga->slug}/{$chapter->slug}") }}"
                                class="ml-1 text-sm font-medium text-gray-700 hover:text-gray-900 md:ml-2 dark:text-gray-400 dark:hover:text-white truncate"
                                itemprop="item" itemtype="http://schema.org/Thing">
                                <span itemprop="name">{{ $chapter->name }}</span>
                            </a>
                            <meta itemprop="position" content="4">
                        </div>
                    </li>
                </ol>
            </div>
            <div
                class="flex py-3 px-5 text-gray-700 bg-gray-50 rounded-lg border border-gray-200 dark:bg-light-blue dark:border-gray-700 shadow-md truncate mb-4">
                <a href="/truyen/{{ $manga->slug }}"
                    class="ml-1 text-xl font-medium text-gray-700 hover:text-gray-900 md:ml-2 dark:text-gray-400 dark:hover:text-white truncate">{{ $manga->name }}</a>
                <span
                    class="ml-1 text-xl font-medium text-gray-700 hover:text-gray-900 md:ml-2 dark:text-gray-400 dark:hover:text-white truncate">-
                    {{ $chapter->name }}</span>
            </div>

            <nav
                class="grid grid-cols-4 mb-4 bg-white dark:bg-light-blue rounded-lg shadow-md dark:shadow-0 border border-gray-200 dark:border-gray-700">
                <a href="{{ $prev_chapter ? "/truyen/{$manga->slug}/{$prev_chapter['slug']}" : 'javascript:void(0)' }}">
                    <button
                        class="flex justify-center py-3 cursor-pointer w-full {{ $prev_chapter ? 'hover:bg-gray-200 dark:hover:bg-gray-600' : 'opacity-40 cursor-not-allowed' }}">
                        <svg class="h-6 w-6 dark:text-white" width="24" height="24" viewBox="0 0 24 24"
                            stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                            stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" />
                            <polyline points="11 7 6 12 11 17" />
                            <polyline points="17 7 12 12 17 17" />
                        </svg>
                        <span class="hidden sm:block">{{ __('Previous Chapter') }}</span>
                    </button>
                </a>
                <a href="/truyen/{{ $manga->slug }}#chapterList">
                    <button
                        class="flex justify-center py-3 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 w-full">
                        <svg class="h-6 w-6 dark:text-white" width="24" height="24" viewBox="0 0 24 24"
                            stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                            stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" />
                            <polyline points="5 12 3 12 12 3 21 12 19 12" />
                            <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" />
                            <rect x="10" y="12" width="4" height="4" />
                        </svg>
                        <span class="hidden sm:block">Home</span>
                    </button>
                </a>
                <li @click="listOpen = !listOpen"
                    class="flex justify-center py-3 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 w-full">
                    <svg class="h-6 w-6 dark:text-white" width="24" height="24" viewBox="0 0 24 24"
                        stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" />
                        <line x1="4" y1="6" x2="20" y2="6" />
                        <line x1="4" y1="12" x2="20" y2="12" />
                        <line x1="4" y1="18" x2="20" y2="18" />
                    </svg>
                    <span class="hidden sm:block">{{ __('List') }}</span>
                </li>
                <a href="{{ $next_chapter ? "/truyen/{$manga->slug}/{$next_chapter['slug']}" : 'javascript:void(0)' }}">
                    <button
                        class="flex justify-center py-3 cursor-pointer w-full {{ $next_chapter ? 'hover:bg-gray-200 dark:hover:bg-gray-600' : 'opacity-40 cursor-not-allowed' }}">
                        <span class="hidden sm:block">{{ __('Next Chapter') }}</span>
                        <svg class="h-6 w-6 dark:text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="13 17 18 12 13 7" />
                            <polyline points="6 17 11 12 6 7" />
                        </svg>
                    </button>
                </a>
            </nav>

            {{-- Chapter content --}}
            <div class="text-center" style="margin: 0 -12px;" id="chapter-content">
                <div class="ads-wrapper text-center">
                </div>
                <img class="chapter-image loaded max-w-full my-0 mx-auto" loading="lazy"
                    src="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgOsSyagVXgSHqAgVX9P2TLMDH8BqJBIIO8_rdnTNc5YTGXGIUlRUGF0p5TvS-tFM1RDC-LRZ5dfGmS3S-kYQzd9h4KmpQwdiwgXPseogvMfbbpp16h7knhTpgnOigy1F1PcPCiersLYNOF6Ipz72dL-UeJUYG-ZTG51iPgiG51prp4cqDYjescaEjqL5M/s16000/dcncc.jpg"
                    alt="{{ $manga->name }} {{ $chapter->name }} - {{ config('app.name') }}">
                @foreach ($chapter->content as $i => $img)
                    <img class="max-w-full my-0 mx-auto" 
                        loading="lazy" 
                        decoding="async"
                        src="{{ $img }}"
                        alt="{{ $manga->name }} {{ $chapter->name }} - Trang {{ $i + 1 }} - {{ config('app.name') }}">
                @endforeach

                {{-- <a href="https://www.facebook.com/groups/1808377903337288" target="_blank">
                    <img class="chapter-image loaded max-w-full my-0 mx-auto" loading="lazy"
                        src="/storage/images/default/groupfb.jpeg"
                        alt="{{ $manga->name }} {{ $chapter->name }} - {{ config('app.name') }}">
                </a> --}}
                <div class="ads-wrapper text-center">
                    {{-- <script data-cfasync="false" type="text/javascript" src="//chaseherbalpasty.com/lv/esnk/2033037/code.js" async class="__clb-2033037"></script> --}}
                </div>
            </div>


            {{-- Chapter Navigation --}}
            <nav
                class="grid grid-cols-4 mb-4 bg-white dark:bg-light-blue rounded-lg shadow-md dark:shadow-0 border border-gray-200 dark:border-gray-700 mt-4">
                <a id="btn-prev"
                    href="{{ $prev_chapter ? "/truyen/{$manga->slug}/{$prev_chapter['slug']}" : 'javascript:nm5213(1)' }}">
                    <button
                        class="flex justify-center py-3 cursor-pointer w-full {{ $prev_chapter ? 'hover:bg-gray-200 dark:hover:bg-gray-600' : 'opacity-40 cursor-not-allowed' }}">
                        <svg class="h-6 w-6 dark:text-white" width="24" height="24" viewBox="0 0 24 24"
                            stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                            stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" />
                            <polyline points="11 7 6 12 11 17" />
                            <polyline points="17 7 12 12 17 17" />
                        </svg>
                        <span class="hidden sm:block">{{ __('Previous Chapter') }}</span>
                    </button>
                </a>
                <a href="/truyen/{{ $manga->slug }}#chapterList">
                    <button
                        class="flex justify-center py-3 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 w-full">
                        <svg class="h-6 w-6 dark:text-white" width="24" height="24" viewBox="0 0 24 24"
                            stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                            stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" />
                            <polyline points="5 12 3 12 12 3 21 12 19 12" />
                            <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" />
                            <rect x="10" y="12" width="4" height="4" />
                        </svg>
                        <span class="hidden sm:block">Home</span>
                    </button>
                </a>
                <li @click="listOpen = !listOpen"
                    class="flex justify-center py-3 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 w-full">
                    <svg class="h-6 w-6 dark:text-white" width="24" height="24" viewBox="0 0 24 24"
                        stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" />
                        <line x1="4" y1="6" x2="20" y2="6" />
                        <line x1="4" y1="12" x2="20" y2="12" />
                        <line x1="4" y1="18" x2="20" y2="18" />
                    </svg>
                    <span class="hidden sm:block">{{ __('List') }}</span>
                </li>
                <a id="btn-next"
                    href="{{ $next_chapter ? "/truyen/{$manga->slug}/{$next_chapter['slug']}" : 'javascript:nm5213(0)' }}">
                    <button
                        class="flex justify-center py-3 cursor-pointer w-full {{ $next_chapter ? 'hover:bg-gray-200 dark:hover:bg-gray-600' : 'opacity-40 cursor-not-allowed' }}">
                        <span class="hidden sm:block">{{ __('Next Chapter') }}</span>
                        <svg class="h-6 w-6 dark:text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="13 17 18 12 13 7" />
                            <polyline points="6 17 11 12 6 7" />
                        </svg>
                    </button>
                </a>
            </nav>

            <div
                class="justify-between border-2 border-gray-100 dark:border-dark-blue p-3 bg-white dark:bg-fire-blue shadow-md rounded dark:shadow-gray-900">
                <div class="flex flex-row truncate mb-4">
                    <svg class="h-6 w-6 dark:text-white" width="24" height="24" viewBox="0 0 24 24"
                        stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" />
                        <path d="M3 20l1.3 -3.9a9 8 0 1 1 3.4 2.9l-4.7 1" />
                        <line x1="12" y1="12" x2="12" y2="12.01" />
                        <line x1="8" y1="12" x2="8" y2="12.01" />
                        <line x1="16" y1="12" x2="16" y2="12.01" />
                    </svg>
                    <span class="grow text-lg ml-1 text-ellipsis font-semibold">{{ __('Comment') }}</span>
                </div>
                @livewire('comment-wrapper', ['class' => \App\Models\Manga::class, 'commentable_id' => $manga->id])
            </div>

            {{-- Chapter List Modal --}}
            <div class="fixed bg-fire-blue/75 h-screen w-screen left-0 top-0 hidden z-50" :class="{ 'hidden': !listOpen }">
                <div
                    class="absolute left-0 top-0 w-full h-full sm:w-1/3 border-r-2 border-gray-100 dark:border-dark-blue pb-3 bg-white dark:bg-fire-blue">
                    <div class="flex gap-2 pb-4 border-b-2 border-gray-100 dark:border-dark-blue p-3">
                        <div class="rounded-md cover-sm" style="background-image: url('{{ $manga->cover_full_url }}')">
                        </div>
                        <div class="flex-auto w-0">
                            <div class="flex">
                                <div class="grow truncate">
                                    <a href="/truyen/{{ $manga->slug }}"
                                        class="text-ellipsis font-semibold hover:text-blue-500">{{ $manga->name }}</a>
                                </div>
                                <div class="cursor-pointer" @click="listOpen=false">
                                    <svg class="h-8 w-8 text-red-500" width="24" height="24" viewBox="0 0 24 24"
                                        stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" />
                                        <line x1="18" y1="6" x2="6" y2="18" />
                                        <line x1="6" y1="6" x2="18" y2="18" />
                                    </svg>
                                </div>

                            </div>
                            <span class="text-sm">
                                {{ $manga->getTrimPilot(10) }}
                            </span>
                        </div>

                    </div>
                    <ul class="max-h-[calc(100vh-107px)] overflow-y-auto overflow-x-hidden p-3">
                        @foreach ($chapters as $i => $c)
                            <a href="/truyen/{{ $manga->slug }}/{{ $c['slug'] }}">
                                <li
                                    class="py-2 px-2 hover:bg-gray-200 dark:hover:bg-light-blue flex gap-3{{ $i % 2 === 0 ? ' bg-gray-100 dark:bg-light-blue' : '' }}{{ $i === $current_index ? ' border-2 border-dashed border-blue-400' : '' }}">
                                    {{ $c['name'] }}</li>
                            </a>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
    </main>
@endsection
@push('additional-script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile chapter navigation scroll handling
            let lastScrollTop = 0;
            const mobileNav = document.getElementById('mobile-chapter-nav');
            let isScrollingDown = false;

            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > lastScrollTop) {
                    // Scrolling down
                    if (!isScrollingDown) {
                        mobileNav.style.opacity = '1';
                        mobileNav.style.transform = 'translateY(0)';
                        isScrollingDown = true;
                    }
                } else {
                    // Scrolling up
                    if (isScrollingDown) {
                        mobileNav.style.opacity = '0';
                        mobileNav.style.transform = 'translateY(-100%)';
                        isScrollingDown = false;
                    }
                }

                lastScrollTop = scrollTop;
            });
        });
    </script>

        <script>
            let chapter_id = '{{ $chapter->id }}';
            let csrf_token = '{{ csrf_token() }}';
        </script>
    <script src="{{ mix('js/read.js') }}" defer></script>
@endpush
