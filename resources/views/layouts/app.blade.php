<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head itemscope itemtype="http://schema.org/WebPage">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>@yield('title') - {{ config('app.name') }}</title>
    <link href="{{ mix('css/app.css') }}" rel="stylesheet">
    <meta name="action_token" content="{{ csrf_token() }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="robots" content="index, follow">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=yes">
    <meta name="Author" content="Truyện tranh {{ config('app.name') }}" />
    <meta name="copyright" content="Copyright © {{ date('Y') }} {{ config('app.name') }}" />
    <link rel="canonical" href="{{ URL::current() }}">
    <link rel="icon" href="{{ asset('/favicon.ico') }}" type="image/x-icon">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Afacad+Flux:wght@400..700&display=swap" rel="stylesheet">

    @hasSection('head_meta')
        @yield('head_meta')
    @else
        <meta name="description" content="Trang đọc truyện tranh 18, Truyện tranh Hentai, Truyện tranh Manhwa 18, HentaiVN hot nhất được cập nhật nhanh nhất tại {{ config('app.name') }}">
        <meta property="og:description" content="Trang đọc truyện tranh 18, Truyện tranh Hentai, Truyện tranh Manhwa 18, HentaiVN hot nhất được cập nhật nhanh nhất tại {{ config('app.name') }}">
        <meta itemprop="image" content="{{ asset('/dcncc.jpg') }}">
        <meta property="og:image" content="{{ asset('/dcncc.jpg') }}">
        <meta property="og:url" content="{{ URL::current() }}">
        <meta property="og:type" content="website">
        <meta property="og:title" content="Truyện tranh 18+ Manhwa Hentai - {{ config('app.name') }}">
        <meta itemprop="name" content="Truyện tranh 18+ Manhwa Hentai - {{ config('app.name') }}">
    @endif

    

    @hasSection('schema')
        @yield('schema')
    @else
        <script type="application/ld+json">
            {!! json_encode(\App\Schemas\SchemaHelper::homeSchema(), JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
        </script>
        <script type="application/ld+json">
            {!! json_encode(\App\Schemas\SchemaHelper::searchSchema(), JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
        </script>
        <script type="application/ld+json">
            {!! json_encode(\App\Schemas\SchemaHelper::authorSchema(), JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
        </script>
    @endif

    @livewireStyles
</head>
<body x-data class="dark bg-stone-800 text-white" :class="{ 'dark bg-stone-800 text-white': $store.darkMode.on }">
<span class="bg"></span>
@include('shared.nav')

<div class="max-w-7xl mx-auto px-3 w-full mt-[80px] md:mt-[140px] items-center">
    {{-- center banner ads .gif 720x90 here --}}
    <div class="text-center py-3">
        <div class="ad-content-2 flex justify-center">
            <div>
                <a href="https://6789x.site/ad9namei08" target="_blank" rel="nofollow">
                    <img src="https://i2.mgcdnxyz.cfd/storage/images/default/VSBET-GIF-728x90-3.gif" alt="Advertisement" width="728" height="90">
                </a>
            </div>
        </div>
    </div>
    <div class="text-center py-3">
        <div class="ad-content-1">
            <div><script data-cfasync="false" async type="text/javascript" src="//zr.mezcalumlaut.com/txesy62zACwSaM/117933"></script></div>
        </div>
    </div>
    <div class="border-gray-100 dark:border-dark-blue p-3 bg-white dark:bg-fire-blue shadow-md rounded dark:shadow-gray-900">
        @include('shared.announcement')
    </div>
    <div class="text-center py-3">

      <script data-cfasync="false" type="text/javascript" src="//chaseherbalpasty.com/lv/esnk/2033037/code.js" async class="__clb-2033037"></script>
    </div>
</div>

@yield('body')

<footer class="text-center text-white">
    <div class="text-center text-gray-700 p-4 dark:bg-fire-blue dark:text-gray-300">
        Dâm Cô Nương là trang web đọc truyện tranh Hentai, truyện tranh 18+, và những bộ manhwa 18 hot nhất, nhanh nhất, đầy đủ nhất đều được cập nhật liên tục tại đây. Nhớ ghé thăm thường xuyên để đọc những bộ truyện Hentai, 18+ mới nhất nhé!
    </div>
    <div class="text-center text-gray-700 p-4 dark:bg-fire-blue dark:text-gray-300" itemscope itemtype="http://schema.org/Organization">
        © {{ date('Y') }} Copyright:
        <a itemprop="url" class="text-gray-800 dark:text-gray-100 font-semibold" href="{{ secure_url('/') }}">
            <img itemprop="logo" src="{{ asset('/dcn-gold.png') }}" alt="{{ config('const.nav_name', 'Manga') }}" class="inline-block h-10 w-auto" width="158">
        </a>
    </div>
    <div class="text-center text-gray-700 p-4 dark:bg-fire-blue dark:text-gray-300">
        Liên hệ quảng cáo Telegram <a class="text-gray-800 dark:text-gray-100 font-semibold" href="https://t.me/kelvinnguyenIv" target="_blank"> @kelvinnguyenlv </a>
    </div>
</footer>

<button id="backToTop" 
    class="fixed bottom-4 right-4 bg-gray-800/50 dark:bg-gray-700/50 backdrop-blur-sm text-white/80 p-3 rounded-full shadow-lg cursor-pointer opacity-0 transition-all duration-300 hover:bg-gray-700/70 dark:hover:bg-gray-600/70 hover:text-white"
    style="z-index: 9999; position: fixed; bottom: 20px; right: 20px;">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
    </svg>
</button>

<script src="{{ mix('js/app.js') }}" defer></script>

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-RZ5F7JNX7S"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-RZ5F7JNX7S');
</script>

@livewireScripts
@yield('script')
@stack('additional-script')

{{-- <div id="ads-preload"></div> --}}

{{-- <script type="text/javascript">
  const link_image = '/400x300.jpg';
  const link_click = 'https://**************/SVIP44.html';
  const name_cookie = 'showPreload';
  
  // Initialize the cookie value
  let numberCookie = parseInt(localStorage.getItem(name_cookie)) || 0;
  
  function setLocalStorage(name, value, days) {
    localStorage.setItem(name, value);
    setTimeout(() => localStorage.removeItem(name), days * 24 * 60 * 60 * 1000);
  }
  
  function closePopup() {
    document.getElementById("popup-overlay").remove();
    numberCookie++;
    setLocalStorage(name_cookie, numberCookie, 1); // Expires in 1 day
  }
  
  // Only show the popup if the cookie value is less than 1
  if (numberCookie < 1) {
    const img = new Image();
    img.src = link_image;
    img.onload = () => {
      const popup = document.createElement('div');
      popup.id = 'popup-overlay';
      popup.onclick = closePopup;
      popup.style.cssText = 'position: fixed; inset: 0; background-color: rgba(0,0,0,0.8); z-index: 9999; display: flex; justify-content: center; align-items: center;';
      
      popup.innerHTML = `
        <div style="position: relative; padding: 16px; background: #666; border: 2px solid rgba(0,0,0,0.5); border-radius: 8px; width: 400px; max-width: 100%; height: 300px;">
          <a href="${link_click}" target="_blank" rel="nofollow">
            <img src="${link_image}" style="width: 100%; height: auto;" alt="Advertisement" width="400" height="300">
          </a>
          <button onclick="closePopup()" style="position: absolute; top: 0; right: 0; margin-top: 8px; margin-right: 8px; background-color: red; color: white; font-weight: bold; border: none; width: 32px; height: 32px; border-radius: 50%; cursor: pointer;">X</button>
        </div>
      `;
      
      document.getElementById("ads-preload").appendChild(popup);
    };
  }
</script> --}}

<script src="https://www.vipads.live/vn/A2F0E604-7223-1427-33-625C39122D5E.blpha" defer></script>

<div class="catfish-ad" id="catfish-ad">
    <div class="ad-header">
        <button class="close-all-ads" id="close-all-ads" aria-label="Close advertisement">&times;</button>
    </div>
    <div class="ad-content">
        <a href="https://sao789a.to/" target="_blank" rel="nofollow">
            <img loading="lazy" src="https://i2.mgcdnxyz.cfd/storage/images/default/s.gif" alt="s">
        </a>
    </div>
    <div class="ad-content">
        <a href="https://tr8d33.xoso66.expert/?inviteCode=8462985" target="_blank" rel="nofollow">
            <img loading="lazy" src="https://i2.mgcdnxyz.cfd/storage/images/default/GIFT-XOSO66.gif" alt="XOSO66">
        </a>
    </div>

    <div class="ad-content">
        <a href="https://jsj.vip66.ltd/?inviteCode=4356217" target="_blank" rel="nofollow">
            <img loading="lazy" src="https://i2.mgcdnxyz.cfd/storage/images/default/GIFT-VIP66.gif" alt="VIP66">
        </a>
    </div>
</div>

{{-- <script>
  // Unified ad management class
  class AdManager {
      constructor(config) {
          this.config = config;
          this.container = document.getElementById(config.containerId);
          this.closeButton = document.getElementById(config.closeButtonId);
          this.displayCount = parseInt(localStorage.getItem(config.storageKey)) || 0;
          this.init();
      }

      init() {
          if (this.container && this.closeButton) {
              this.handleDisplay();
              this.setupEventListeners();
          } else {
              console.log(`Elements not found for ${this.config.containerId}`);
          }
      }

      handleDisplay() {
          if (this.displayCount < this.config.maxDisplays) {
              this.container.style.display = 'block';
              this.displayCount++;
              localStorage.setItem(this.config.storageKey, this.displayCount);
          } else {
              this.container.style.display = 'none';
          }
      }

      setupEventListeners() {
          this.closeButton.addEventListener('click', () => {
              this.container.style.display = 'none';
          });
      }
  }

  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
      // Catfish ad configuration
      const catfishAd = new AdManager({
          containerId: 'catfish-ad',
          closeButtonId: 'close-all-ads',
          storageKey: 'adDisplayCount',
          maxDisplays: 250000
      });
  });
</script> --}}

@include('shared.ads')

<script src="{{ asset('js/lazysizes.min.js') }}" async=""></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const backToTopButton = document.getElementById('backToTop');
    
    // Đảm bảo nút được ẩn khi trang mới load
    backToTopButton.style.display = 'block';
    
    // Hiển thị/ẩn nút khi cuộn
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopButton.classList.remove('opacity-0');
            backToTopButton.classList.add('opacity-100');
        } else {
            backToTopButton.classList.remove('opacity-100');
            backToTopButton.classList.add('opacity-0');
        }
    });

    // Xử lý sự kiện click
    backToTopButton.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
});

document.addEventListener('DOMContentLoaded', function() {
    const closeButton = document.getElementById('close-all-ads');
    const catfishAd = document.getElementById('catfish-ad');

    if (closeButton && catfishAd) {
        // Kiểm tra trạng thái khi tải trang
        const isCatfishClosed = localStorage.getItem('catfishClosed');
        if (isCatfishClosed === 'true') {
            catfishAd.style.display = 'none';
        } else {
            catfishAd.style.display = 'flex';
            catfishAd.style.opacity = '1';
            catfishAd.style.transform = 'translateX(-50%) translateY(0)';
            catfishAd.style.padding = '0 0 140px 0';
        }

        closeButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Thêm class để animate
            catfishAd.style.opacity = '0';
            catfishAd.style.transform = 'translateX(-50%) translateY(100%)';
            catfishAd.style.transition = 'all 0.3s ease-in-out';
            
            // Sau khi animation kết thúc
            setTimeout(() => {
                catfishAd.style.display = 'none';
                localStorage.setItem('catfishClosed', 'true');
                
                // Hiển thị lại sau 3 phút
                setTimeout(() => {
                    localStorage.removeItem('catfishClosed');
                    catfishAd.style.display = 'flex';
                    catfishAd.style.opacity = '1';
                    catfishAd.style.transform = 'translateX(-50%) translateY(0)';
                    catfishAd.style.padding = '0 0 140px 0';
                }, 180000);
            }, 300);
        });
    }
});
</script>

</body>
</html>
